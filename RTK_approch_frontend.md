# ⚙️ RTK Query Integration Structure & Flow (React + Vite + Redux Toolkit)

This guide outlines how to **integrate RTK Query** into an existing React + Vite project using Redux Toolkit — focusing on **project structure** and **data flow**, not implementation code.

---

## 📁 Folder Structure

src/
├── app/
│ └── store.js # Redux store with RTK Query integration
├── services/
│ └── api.js # Centralized API slice (RTK Query)
├── features/
│ └── [sliceName]/ # Existing Redux slices
│ └── slice.js
├── components/
│ └── [ComponentName].jsx # React components using API hooks


---

## 🔄 RTK Query Data Flow

### 1. Define API Slice

- Centralized API logic lives in `services/api.js`
- Declare all endpoints (queries & mutations)
- Set up base URL and caching behavior

### 2. Inject API Into Store

- Add the API reducer to the root reducer
- Include API middleware
- Coexists with other feature slices in Redux

### 3. Use Auto-Generated Hooks in Components

- Use `use[QueryName]Query` or `use[MutationName]Mutation`
- Provides:
  - `data`
  - `isLoading`
  - `isError`
  - `refetch` & caching options

### 4. Automatic Caching & Deduplication

- RTK Query caches based on query key + args
- Only one request per unique argument, even across re-renders or multiple components
- Eliminates need for manual caching or global request state

### 5. Optional: Cache Invalidation

- Use `tagTypes`, `providesTags`, `invalidatesTags`
- Helps re-fetch relevant data after mutations

---

## ✅ Summary Table

| Step                      | Responsibility                         |
|---------------------------|-----------------------------------------|
| `services/api.js`         | Define endpoints (queries/mutations)    |
| `app/store.js`            | Register API reducer + middleware       |
| `components/`             | Use generated API hooks in components   |
| `features/`               | Keep current Redux logic as-is          |

---

## 🧠 Key Benefits

- ✅ One API call per unique endpoint/args
- ✅ Reuse data across components
- ✅ Caching, loading, error states managed automatically
- ✅ Clean, declarative, component-based data fetching
- ✅ Fully compatible with your existing Redux state

---

## 📌 Notes

- Do not use `useEffect` + `fetch()` with RTK Query-managed endpoints.
- RTK Query is optimized for **server state**, while Redux slices remain ideal for **UI/client state**.

---

## 🧩 Next Steps

- Add your API endpoints to `services/api.js`
- Start using auto-generated hooks in your UI
- Optionally set up cache invalidation for mutations

