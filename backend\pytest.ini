[tool:pytest]
DJANGO_SETTINGS_MODULE = chatapp.settings_test
django_find_project = false
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=.
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=100
    --reuse-db
    --nomigrations
testpaths = .
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    auth: Authentication related tests
    messaging: Messaging related tests
    api: API endpoint tests
    models: Model tests
    schemas: Schema validation tests
