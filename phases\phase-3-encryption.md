# Phase 3: End-to-End Encryption

**Duration**: 2-3 weeks | **Priority**: High

## Overview
This phase implements end-to-end encryption for all messages using the Signal Protocol, ensuring that only intended recipients can read message content. The server stores only encrypted data and cannot decrypt messages.

## Prerequisites
- Phase 2 completed successfully
- Core messaging functionality working
- User authentication system stable
- Understanding of cryptographic concepts
- Web Crypto API support in target browsers

## Encryption Architecture

### Signal Protocol Implementation
We'll implement a simplified version of the Signal Protocol with:
- **Identity Keys**: Long-term key pairs for each user
- **Signed Pre-keys**: Medium-term keys signed by identity keys
- **One-time Pre-keys**: Short-term keys for perfect forward secrecy
- **Session Keys**: Ephemeral keys for message encryption
- **Double Ratchet**: For forward secrecy and self-healing

### Key Management Flow
1. User generates identity key pair on registration
2. User generates and uploads signed pre-keys and one-time pre-keys
3. When starting a conversation, users exchange keys
4. Messages are encrypted with session keys derived from exchanged keys
5. Keys are rotated regularly for forward secrecy

## Database Schema Updates

### Step 1: Create Encryption Models

```python
# backend/apps/encryption/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class UserKeyBundle(models.Model):
    """Stores user's public keys for key exchange"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='key_bundle')
    identity_public_key = models.TextField()  # Base64 encoded public key
    signed_prekey_id = models.IntegerField()
    signed_prekey_public = models.TextField()  # Base64 encoded
    signed_prekey_signature = models.TextField()  # Base64 encoded
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_key_bundles'

class OneTimePreKey(models.Model):
    """One-time pre-keys for perfect forward secrecy"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='one_time_prekeys')
    key_id = models.IntegerField()
    public_key = models.TextField()  # Base64 encoded
    is_used = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'one_time_prekeys'
        unique_together = ['user', 'key_id']

class ConversationSession(models.Model):
    """Stores session state for each participant in a conversation"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey('messaging.Conversation', on_delete=models.CASCADE, related_name='sessions')
    participant = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_sessions')
    session_state = models.JSONField()  # Encrypted session state
    root_key = models.TextField()  # Base64 encoded, encrypted with user's key
    chain_key_send = models.TextField(null=True, blank=True)  # Base64 encoded
    chain_key_receive = models.TextField(null=True, blank=True)  # Base64 encoded
    message_number_send = models.IntegerField(default=0)
    message_number_receive = models.IntegerField(default=0)
    previous_chain_length = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'conversation_sessions'
        unique_together = ['conversation', 'participant']

class MessageKey(models.Model):
    """Stores message keys for delayed decryption"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(ConversationSession, on_delete=models.CASCADE, related_name='message_keys')
    message_number = models.IntegerField()
    message_key = models.TextField()  # Base64 encoded, encrypted
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'message_keys'
        unique_together = ['session', 'message_number']
```

### Step 2: Update Message Model

```python
# backend/apps/messaging/models.py - Add to existing Message model
class Message(models.Model):
    # ... existing fields ...
    
    # Encryption fields
    encrypted_content = models.TextField()  # Base64 encoded encrypted content
    message_key_id = models.TextField(null=True, blank=True)  # For key identification
    sender_ratchet_key = models.TextField(null=True, blank=True)  # Base64 encoded
    message_number = models.IntegerField(default=0)
    previous_chain_length = models.IntegerField(default=0)
    
    # Remove or deprecate the plain content field
    content = models.TextField(blank=True)  # Keep for backward compatibility, will be empty
    
    class Meta:
        db_table = 'messages'
        ordering = ['created_at']
```

## Cryptographic Implementation

### Step 3: Client-Side Crypto Utilities

```typescript
// frontend/src/utils/crypto.ts
export class CryptoUtils {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 256;
  private static readonly IV_LENGTH = 12;

  /**
   * Generate a new key pair for identity or ephemeral keys
   */
  static async generateKeyPair(): Promise<CryptoKeyPair> {
    return await window.crypto.subtle.generateKey(
      {
        name: 'ECDH',
        namedCurve: 'P-256',
      },
      true, // extractable
      ['deriveKey', 'deriveBits']
    );
  }

  /**
   * Generate a signing key pair for signed pre-keys
   */
  static async generateSigningKeyPair(): Promise<CryptoKeyPair> {
    return await window.crypto.subtle.generateKey(
      {
        name: 'ECDSA',
        namedCurve: 'P-256',
      },
      true,
      ['sign', 'verify']
    );
  }

  /**
   * Export public key to base64 string
   */
  static async exportPublicKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('spki', key);
    return btoa(String.fromCharCode(...new Uint8Array(exported)));
  }

  /**
   * Import public key from base64 string
   */
  static async importPublicKey(keyData: string, algorithm: string): Promise<CryptoKey> {
    const binaryKey = Uint8Array.from(atob(keyData), c => c.charCodeAt(0));
    return await window.crypto.subtle.importKey(
      'spki',
      binaryKey,
      algorithm === 'ECDH' ? { name: 'ECDH', namedCurve: 'P-256' } : { name: 'ECDSA', namedCurve: 'P-256' },
      false,
      algorithm === 'ECDH' ? ['deriveKey', 'deriveBits'] : ['verify']
    );
  }

  /**
   * Derive shared secret from key pair
   */
  static async deriveSharedSecret(privateKey: CryptoKey, publicKey: CryptoKey): Promise<ArrayBuffer> {
    return await window.crypto.subtle.deriveBits(
      {
        name: 'ECDH',
        public: publicKey,
      },
      privateKey,
      256
    );
  }

  /**
   * Derive encryption key from shared secret
   */
  static async deriveEncryptionKey(sharedSecret: ArrayBuffer, salt: Uint8Array): Promise<CryptoKey> {
    const keyMaterial = await window.crypto.subtle.importKey(
      'raw',
      sharedSecret,
      'HKDF',
      false,
      ['deriveKey']
    );

    return await window.crypto.subtle.deriveKey(
      {
        name: 'HKDF',
        hash: 'SHA-256',
        salt: salt,
        info: new TextEncoder().encode('ChatApp Message Key'),
      },
      keyMaterial,
      { name: this.ALGORITHM, length: this.KEY_LENGTH },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt message content
   */
  static async encryptMessage(content: string, key: CryptoKey): Promise<{
    encryptedData: string;
    iv: string;
  }> {
    const iv = window.crypto.getRandomValues(new Uint8Array(this.IV_LENGTH));
    const encodedContent = new TextEncoder().encode(content);

    const encryptedBuffer = await window.crypto.subtle.encrypt(
      {
        name: this.ALGORITHM,
        iv: iv,
      },
      key,
      encodedContent
    );

    return {
      encryptedData: btoa(String.fromCharCode(...new Uint8Array(encryptedBuffer))),
      iv: btoa(String.fromCharCode(...iv)),
    };
  }

  /**
   * Decrypt message content
   */
  static async decryptMessage(encryptedData: string, iv: string, key: CryptoKey): Promise<string> {
    const encryptedBuffer = Uint8Array.from(atob(encryptedData), c => c.charCodeAt(0));
    const ivBuffer = Uint8Array.from(atob(iv), c => c.charCodeAt(0));

    const decryptedBuffer = await window.crypto.subtle.decrypt(
      {
        name: this.ALGORITHM,
        iv: ivBuffer,
      },
      key,
      encryptedBuffer
    );

    return new TextDecoder().decode(decryptedBuffer);
  }

  /**
   * Generate random bytes
   */
  static generateRandomBytes(length: number): Uint8Array {
    return window.crypto.getRandomValues(new Uint8Array(length));
  }

  /**
   * Hash data using SHA-256
   */
  static async hash(data: string): Promise<string> {
    const encoded = new TextEncoder().encode(data);
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', encoded);
    return btoa(String.fromCharCode(...new Uint8Array(hashBuffer)));
  }
}
```

### Step 4: Signal Protocol Implementation

```typescript
// frontend/src/utils/signalProtocol.ts
import { CryptoUtils } from './crypto';

export interface KeyBundle {
  identityKey: string;
  signedPreKey: {
    id: number;
    publicKey: string;
    signature: string;
  };
  oneTimePreKey?: {
    id: number;
    publicKey: string;
  };
}

export interface SessionState {
  rootKey: string;
  chainKeySend?: string;
  chainKeyReceive?: string;
  messageNumberSend: number;
  messageNumberReceive: number;
  previousChainLength: number;
  ratchetKeyPair?: {
    private: string;
    public: string;
  };
  remoteRatchetKey?: string;
}

export class SignalProtocol {
  private identityKeyPair: CryptoKeyPair | null = null;
  private signedPreKeyPair: CryptoKeyPair | null = null;
  private oneTimePreKeys: Map<number, CryptoKeyPair> = new Map();

  /**
   * Initialize user's key pairs
   */
  async initializeKeys(): Promise<void> {
    // Generate identity key pair
    this.identityKeyPair = await CryptoUtils.generateKeyPair();
    
    // Generate signed pre-key pair
    this.signedPreKeyPair = await CryptoUtils.generateKeyPair();
    
    // Generate one-time pre-keys
    for (let i = 0; i < 100; i++) {
      const keyPair = await CryptoUtils.generateKeyPair();
      this.oneTimePreKeys.set(i, keyPair);
    }
  }

  /**
   * Get user's public key bundle for sharing
   */
  async getKeyBundle(): Promise<KeyBundle> {
    if (!this.identityKeyPair || !this.signedPreKeyPair) {
      throw new Error('Keys not initialized');
    }

    const identityPublicKey = await CryptoUtils.exportPublicKey(this.identityKeyPair.publicKey);
    const signedPreKeyPublic = await CryptoUtils.exportPublicKey(this.signedPreKeyPair.publicKey);
    
    // Sign the pre-key with identity key
    const signedPreKeyData = new TextEncoder().encode(signedPreKeyPublic);
    const signature = await window.crypto.subtle.sign(
      { name: 'ECDSA', hash: 'SHA-256' },
      this.identityKeyPair.privateKey,
      signedPreKeyData
    );

    // Get a one-time pre-key
    const oneTimePreKeyId = Array.from(this.oneTimePreKeys.keys())[0];
    const oneTimePreKeyPair = this.oneTimePreKeys.get(oneTimePreKeyId);
    let oneTimePreKey;
    
    if (oneTimePreKeyPair) {
      oneTimePreKey = {
        id: oneTimePreKeyId,
        publicKey: await CryptoUtils.exportPublicKey(oneTimePreKeyPair.publicKey),
      };
    }

    return {
      identityKey: identityPublicKey,
      signedPreKey: {
        id: 1,
        publicKey: signedPreKeyPublic,
        signature: btoa(String.fromCharCode(...new Uint8Array(signature))),
      },
      oneTimePreKey,
    };
  }

  /**
   * Initialize session with remote user's key bundle
   */
  async initializeSession(remoteKeyBundle: KeyBundle): Promise<SessionState> {
    if (!this.identityKeyPair) {
      throw new Error('Identity key not initialized');
    }

    // Import remote keys
    const remoteIdentityKey = await CryptoUtils.importPublicKey(remoteKeyBundle.identityKey, 'ECDH');
    const remoteSignedPreKey = await CryptoUtils.importPublicKey(remoteKeyBundle.signedPreKey.publicKey, 'ECDH');
    
    let remoteOneTimePreKey;
    if (remoteKeyBundle.oneTimePreKey) {
      remoteOneTimePreKey = await CryptoUtils.importPublicKey(remoteKeyBundle.oneTimePreKey.publicKey, 'ECDH');
    }

    // Generate ephemeral key pair
    const ephemeralKeyPair = await CryptoUtils.generateKeyPair();

    // Perform Triple Diffie-Hellman
    const dh1 = await CryptoUtils.deriveSharedSecret(this.identityKeyPair.privateKey, remoteSignedPreKey);
    const dh2 = await CryptoUtils.deriveSharedSecret(ephemeralKeyPair.privateKey, remoteIdentityKey);
    const dh3 = await CryptoUtils.deriveSharedSecret(ephemeralKeyPair.privateKey, remoteSignedPreKey);
    
    let dh4;
    if (remoteOneTimePreKey) {
      dh4 = await CryptoUtils.deriveSharedSecret(ephemeralKeyPair.privateKey, remoteOneTimePreKey);
    }

    // Combine shared secrets
    const sharedSecrets = [dh1, dh2, dh3];
    if (dh4) sharedSecrets.push(dh4);

    const combinedSecret = await this.combineSharedSecrets(sharedSecrets);
    
    // Derive root key
    const salt = CryptoUtils.generateRandomBytes(32);
    const rootKey = await CryptoUtils.deriveEncryptionKey(combinedSecret, salt);

    return {
      rootKey: await this.exportKey(rootKey),
      messageNumberSend: 0,
      messageNumberReceive: 0,
      previousChainLength: 0,
      ratchetKeyPair: {
        private: await this.exportPrivateKey(ephemeralKeyPair.privateKey),
        public: await CryptoUtils.exportPublicKey(ephemeralKeyPair.publicKey),
      },
    };
  }

  /**
   * Encrypt message using session state
   */
  async encryptMessage(content: string, sessionState: SessionState): Promise<{
    encryptedContent: string;
    iv: string;
    messageNumber: number;
    ratchetKey: string;
  }> {
    // Derive message key from chain key
    const chainKey = sessionState.chainKeySend || sessionState.rootKey;
    const messageKey = await this.deriveMessageKey(chainKey, sessionState.messageNumberSend);
    
    // Encrypt message
    const { encryptedData, iv } = await CryptoUtils.encryptMessage(content, messageKey);
    
    // Update session state
    sessionState.messageNumberSend++;
    
    return {
      encryptedContent: encryptedData,
      iv,
      messageNumber: sessionState.messageNumberSend - 1,
      ratchetKey: sessionState.ratchetKeyPair?.public || '',
    };
  }

  /**
   * Decrypt message using session state
   */
  async decryptMessage(
    encryptedContent: string,
    iv: string,
    messageNumber: number,
    sessionState: SessionState
  ): Promise<string> {
    // Derive message key
    const chainKey = sessionState.chainKeyReceive || sessionState.rootKey;
    const messageKey = await this.deriveMessageKey(chainKey, messageNumber);
    
    // Decrypt message
    const decryptedContent = await CryptoUtils.decryptMessage(encryptedContent, iv, messageKey);
    
    // Update session state
    if (messageNumber >= sessionState.messageNumberReceive) {
      sessionState.messageNumberReceive = messageNumber + 1;
    }
    
    return decryptedContent;
  }

  // Helper methods
  private async combineSharedSecrets(secrets: ArrayBuffer[]): Promise<ArrayBuffer> {
    const combined = new Uint8Array(secrets.reduce((acc, secret) => acc + secret.byteLength, 0));
    let offset = 0;
    
    for (const secret of secrets) {
      combined.set(new Uint8Array(secret), offset);
      offset += secret.byteLength;
    }
    
    return combined.buffer;
  }

  private async deriveMessageKey(chainKey: string, messageNumber: number): Promise<CryptoKey> {
    const keyData = Uint8Array.from(atob(chainKey), c => c.charCodeAt(0));
    const salt = new TextEncoder().encode(`message_${messageNumber}`);
    
    return await CryptoUtils.deriveEncryptionKey(keyData.buffer, salt);
  }

  private async exportKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('raw', key);
    return btoa(String.fromCharCode(...new Uint8Array(exported)));
  }

  private async exportPrivateKey(key: CryptoKey): Promise<string> {
    const exported = await window.crypto.subtle.exportKey('pkcs8', key);
    return btoa(String.fromCharCode(...new Uint8Array(exported)));
  }
}
```

## API Integration

### Step 5: Encryption API Endpoints

```python
# backend/apps/encryption/views.py
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from .models import UserKeyBundle, OneTimePreKey
from .serializers import KeyBundleSerializer, OneTimePreKeySerializer

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def upload_key_bundle(request):
    """Upload user's public key bundle"""
    serializer = KeyBundleSerializer(data=request.data)
    if serializer.is_valid():
        # Delete existing key bundle
        UserKeyBundle.objects.filter(user=request.user).delete()
        
        # Create new key bundle
        key_bundle = serializer.save(user=request.user)
        
        return Response({
            'message': 'Key bundle uploaded successfully',
            'key_bundle_id': str(key_bundle.id)
        }, status=status.HTTP_201_CREATED)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_key_bundle(request, user_id):
    """Get another user's public key bundle for key exchange"""
    try:
        key_bundle = UserKeyBundle.objects.get(user_id=user_id)
        
        # Get an unused one-time pre-key
        one_time_prekey = OneTimePreKey.objects.filter(
            user_id=user_id,
            is_used=False
        ).first()
        
        if one_time_prekey:
            # Mark as used
            one_time_prekey.is_used = True
            one_time_prekey.save()
        
        response_data = KeyBundleSerializer(key_bundle).data
        if one_time_prekey:
            response_data['one_time_prekey'] = OneTimePreKeySerializer(one_time_prekey).data
        
        return Response(response_data)
        
    except UserKeyBundle.DoesNotExist:
        return Response(
            {'error': 'Key bundle not found for user'},
            status=status.HTTP_404_NOT_FOUND
        )

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def upload_one_time_prekeys(request):
    """Upload batch of one-time pre-keys"""
    prekeys_data = request.data.get('prekeys', [])
    
    created_prekeys = []
    for prekey_data in prekeys_data:
        serializer = OneTimePreKeySerializer(data=prekey_data)
        if serializer.is_valid():
            prekey = serializer.save(user=request.user)
            created_prekeys.append(prekey)
    
    return Response({
        'message': f'{len(created_prekeys)} one-time pre-keys uploaded',
        'count': len(created_prekeys)
    }, status=status.HTTP_201_CREATED)
```

## Integration Points

### Frontend ↔ Backend
- Key bundle upload/download
- Encrypted message storage
- Session state management

### Message Flow
1. Sender encrypts message with session key
2. Encrypted message sent to server
3. Server stores encrypted content
4. Recipients download and decrypt locally

## Acceptance Criteria

### Phase 3 Completion Checklist
- [ ] Signal Protocol implementation working
- [ ] Key generation and exchange functional
- [ ] Message encryption/decryption working
- [ ] Session state management implemented
- [ ] Key bundle API endpoints functional
- [ ] Forward secrecy maintained
- [ ] Server cannot decrypt messages

### Testing Requirements
- [ ] Cryptographic function unit tests
- [ ] Key exchange integration tests
- [ ] Message encryption/decryption tests
- [ ] Session state persistence tests
- [ ] Security audit of implementation

## Common Issues & Troubleshooting

### Key Exchange Failures
- Verify key format compatibility
- Check signature validation
- Ensure proper key derivation

### Encryption/Decryption Errors
- Validate IV generation and storage
- Check key derivation consistency
- Verify message format integrity

### Performance Issues
- Use Web Workers for crypto operations
- Implement key caching strategies
- Optimize session state updates

## Next Phase Dependencies
- End-to-end encryption fully functional
- Key management system stable
- Message encryption transparent to users
- Performance acceptable for real-time chat

This phase ensures message privacy and security. Thorough testing is critical before proceeding to Phase 4 (Group Chat).
