import React, { useState } from 'react';
import {
  useInitiateCallMutation,
  useAnswerCallMutation,
  useDeclineCallMutation,
  useEndCallMutation,
  useGetCallHistoryQuery,
  useGetCallDetailQuery,
} from '../services/callingApi';

interface CallingTestProps {
  conversationId?: string;
}

const CallingTest: React.FC<CallingTestProps> = ({ 
  conversationId = '03a75579-9f70-4deb-b960-2221c9fa1e97' // Default to existing conversation
}) => {
  const [callId, setCallId] = useState<string>('');
  const [callType, setCallType] = useState<'audio' | 'video'>('audio');
  
  // RTK Query hooks
  const [initiateCall, { isLoading: isInitiating, error: initiateError }] = useInitiateCallMutation();
  const [answerCall, { isLoading: isAnswering, error: answerError }] = useAnswerCallMutation();
  const [declineCall, { isLoading: isDeclining, error: declineError }] = useDeclineCallMutation();
  const [endCall, { isLoading: isEnding, error: endError }] = useEndCallMutation();
  
  const { data: callHistory, isLoading: isLoadingHistory, error: historyError } = useGetCallHistoryQuery();
  const { data: callDetail, isLoading: isLoadingDetail, error: detailError } = useGetCallDetailQuery(callId, {
    skip: !callId
  });

  const handleInitiateCall = async () => {
    try {
        const result = await initiateCall({
          conversationId: conversationId,
          callType: callType,
        }).unwrap();
      
      setCallId(result.id);
      console.log('✅ Call initiated:', result);
    } catch (error) {
      console.error('❌ Failed to initiate call:', error);
    }
  };

  const handleAnswerCall = async () => {
    if (!callId) return;
    
    try {
      const result = await answerCall(callId).unwrap();
      console.log('✅ Call answered:', result);
    } catch (error) {
      console.error('❌ Failed to answer call:', error);
    }
  };

  const handleDeclineCall = async () => {
    if (!callId) return;
    
    try {
      const result = await declineCall(callId).unwrap();
      console.log('✅ Call declined:', result);
      setCallId(''); // Clear call ID after declining
    } catch (error) {
      console.error('❌ Failed to decline call:', error);
    }
  };

  const handleEndCall = async () => {
    if (!callId) return;
    
    try {
      const result = await endCall(callId).unwrap();
      console.log('✅ Call ended:', result);
      setCallId(''); // Clear call ID after ending
    } catch (error) {
      console.error('❌ Failed to end call:', error);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Calling API Test</h2>
      
      {/* Call Controls */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 className="text-lg font-semibold mb-4">Call Controls</h3>
        
        <div className="flex flex-wrap gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Call Type:
            </label>
            <select 
              value={callType} 
              onChange={(e) => setCallType(e.target.value as 'audio' | 'video')}
              className="border border-gray-300 rounded px-3 py-2"
            >
              <option value="audio">Audio</option>
              <option value="video">Video</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Conversation ID:
            </label>
            <input 
              type="text" 
              value={conversationId}
              readOnly
              className="border border-gray-300 rounded px-3 py-2 bg-gray-50"
            />
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <button
            onClick={handleInitiateCall}
            disabled={isInitiating}
            className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
          >
            {isInitiating ? 'Initiating...' : 'Initiate Call'}
          </button>
          
          <button
            onClick={handleAnswerCall}
            disabled={!callId || isAnswering}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
          >
            {isAnswering ? 'Answering...' : 'Answer Call'}
          </button>
          
          <button
            onClick={handleDeclineCall}
            disabled={!callId || isDeclining}
            className="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
          >
            {isDeclining ? 'Declining...' : 'Decline Call'}
          </button>
          
          <button
            onClick={handleEndCall}
            disabled={!callId || isEnding}
            className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white px-4 py-2 rounded"
          >
            {isEnding ? 'Ending...' : 'End Call'}
          </button>
        </div>
        
        {callId && (
          <div className="mt-4 p-3 bg-blue-50 rounded">
            <strong>Current Call ID:</strong> {callId}
          </div>
        )}
        
        {/* Error Display */}
        {(initiateError || answerError || declineError || endError) && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
            <strong>Error:</strong>
            <pre className="text-sm mt-1">
              {JSON.stringify(initiateError || answerError || declineError || endError, null, 2)}
            </pre>
          </div>
        )}
      </div>
      
      {/* Call Detail */}
      {callId && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Current Call Details</h3>
          {isLoadingDetail ? (
            <p>Loading call details...</p>
          ) : detailError ? (
            <div className="text-red-600">
              Error loading call details: {JSON.stringify(detailError)}
            </div>
          ) : callDetail ? (
            <div className="space-y-2">
              <p><strong>Status:</strong> {callDetail.status}</p>
              <p><strong>Type:</strong> {callDetail.call_type}</p>
              <p><strong>Caller:</strong> {callDetail.caller.first_name} {callDetail.caller.last_name}</p>
              <p><strong>Callee:</strong> {callDetail.callee.first_name} {callDetail.callee.last_name}</p>
              <p><strong>Initiated:</strong> {new Date(callDetail.initiated_at).toLocaleString()}</p>
              {callDetail.duration && <p><strong>Duration:</strong> {callDetail.duration}</p>}
            </div>
          ) : null}
        </div>
      )}
      
      {/* Call History */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold mb-4">Call History</h3>
        {isLoadingHistory ? (
          <p>Loading call history...</p>
        ) : historyError ? (
          <div className="text-red-600">
            Error loading call history: {JSON.stringify(historyError)}
          </div>
        ) : callHistory && callHistory.length > 0 ? (
          <div className="space-y-4">
            {callHistory.slice(0, 5).map((call) => (
              <div key={call.id} className="border border-gray-200 rounded p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <p><strong>Type:</strong> {call.call_type}</p>
                    <p><strong>Status:</strong> {call.status}</p>
                    <p><strong>Participants:</strong> {call.caller.first_name} → {call.callee.first_name}</p>
                    <p><strong>Date:</strong> {new Date(call.initiated_at).toLocaleString()}</p>
                    {call.duration && <p><strong>Duration:</strong> {call.duration}</p>}
                  </div>
                  <button
                    onClick={() => setCallId(call.id)}
                    className="text-blue-500 hover:text-blue-700 text-sm"
                  >
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No call history found.</p>
        )}
      </div>
    </div>
  );
};

export default CallingTest;