// frontend/src/components/Chat/ChatHeader.tsx
import React from 'react';
import { useSelector } from 'react-redux';
import { User, MoreVertical } from 'lucide-react';
import { CallControls } from '../Call/CallControls';
import { useGetConversationQuery } from '../../services/conversationApi';
import type { RootState } from '../../store';
import type { Conversation, DraftConversation } from '../../store/slices/conversationSlice';

interface ChatHeaderProps {
  conversationId: string;
  currentUserId: string;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({ conversationId, currentUserId }) => {
  const isDraftConversation = conversationId.startsWith('draft-');
  
  // Get conversation from RTK Query for real conversations
  const { data: apiResponse, isLoading: isApiLoading } = useGetConversationQuery(conversationId, {
    skip: !conversationId || isDraftConversation
  });
  
  // Get conversation from Redux store for draft conversations
  const draftConversation = useSelector((state: RootState) => 
    state.conversations.draftConversations.find(draft => draft.id === conversationId)
  );
  
  // Get real conversation from Redux store as fallback
  const reduxConversation = useSelector((state: RootState) => 
    state.conversations.conversations.find(conv => conv.id === conversationId)
  );
  
  // Determine the conversation data and loading state
  let conversation: Conversation | DraftConversation | undefined;
  let isLoading: boolean;
  
  if (isDraftConversation) {
    conversation = draftConversation;
    isLoading = false;
  } else {
    // For real conversations, prefer API response, fallback to Redux
    conversation = apiResponse?.success ? apiResponse.data : reduxConversation;
    isLoading = isApiLoading;
  }

  console.log('conversation1111111111111', {
    conversationId,
    isDraftConversation,
    apiResponse,
    draftConversation,
    reduxConversation,
    finalConversation: conversation,
    isLoading
  });

  if (isLoading || !conversation) {
    return (
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
            <div>
              <div className="w-24 h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="w-16 h-3 bg-gray-200 rounded animate-pulse mt-1"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Get the other participant for direct conversations
  // Handle both real conversations (with user nested structure) and draft conversations (flat structure)
  const otherParticipant = conversation.type === 'DIRECT' 
    ? conversation.participants.find(p => {
        // For real conversations, participants have nested user object
        const userId = p.user?.id || p.id;
        return userId !== currentUserId;
      })
    : null;

  const getDisplayName = () => {
    if (conversation.type === 'GROUP') {
      return conversation.name || 'Group Chat';
    }
    
    if (otherParticipant) {
      // Handle both nested user structure and flat structure
      const user = otherParticipant.user || otherParticipant;
      const { first_name, last_name, username } = user;
      if (first_name || last_name) {
        return `${first_name || ''} ${last_name || ''}`.trim();
      }
      return username;
    }
    
    return 'Unknown';
  };

  const getSubtitle = () => {
    if (conversation.type === 'GROUP') {
      // For draft conversations, participants don't have is_active property
      const participantCount = conversation.participants.length;
      return `${participantCount} members`;
    }
    
    // For direct conversations, show appropriate status
    if (isDraftConversation) {
      return 'New conversation';
    }
    return 'Direct message';
  };

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Conversation info */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
            {conversation.avatar_url ? (
              <img
                src={conversation.avatar_url}
                alt={getDisplayName()}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <User className="w-6 h-6 text-gray-600" />
            )}
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">{getDisplayName()}</h2>
            <p className="text-sm text-gray-500">{getSubtitle()}</p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2">
          {/* Call controls - only show for real direct conversations (not drafts) */}
          {conversation.type === 'DIRECT' && otherParticipant && !isDraftConversation && (() => {
            const user = otherParticipant.user || otherParticipant;
            return (
              <div className="flex items-center space-x-2">
                <CallControls
                  conversationId={conversationId}
                  otherParticipant={{
                    id: user.id,
                    username: user.username,
                    firstName: user.first_name || '',
                    lastName: user.last_name || '',
                    profilePicture: user.profile_picture,
                  }}
                />
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">
                  API TEST
                </span>
              </div>
            );
          })()}

          {/* More options */}
          <button
            className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            title="More options"
          >
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
};
