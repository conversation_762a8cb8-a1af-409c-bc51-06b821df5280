# Chat Application Setup Instructions

## Phase 1: Foundation & Infrastructure - COMPLETED ✅

This document provides setup instructions for the chat application foundation that has been implemented according to the phase-1-foundation-infrastructure.md specifications.

## Architecture Overview

The application consists of three main components:

1. **Django Backend** (`/backend`) - REST API with JWT authentication
2. **React Frontend** (`/frontend`) - TypeScript React app with custom UI components
3. **Socket.io Server** (`/socket-server`) - Real-time messaging with Prisma ORM

## Technology Stack

### Backend (Django)
- **Framework**: Django 5.2.4 with Django REST Framework
- **Database**: PostgreSQL with custom User model
- **Authentication**: JWT tokens using djangorestframework-simplejwt
- **Validation**: Pydantic schemas for request/response validation
- **Cache**: Redis for session management

### Frontend (React with TypeScript)
- **Framework**: React 18.2.0 with TypeScript
- **Build Tool**: Vite 5.4.19 (migrated from Create React App for better performance)
- **Styling**: Tailwind CSS with custom components
- **UI Components**: Custom components using Lucide React icons
- **Routing**: React Router DOM for navigation and protected routes
- **State Management**: Context API for authentication
- **HTTP Client**: Axios for API requests
- **Real-time**: Socket.io-client for WebSocket connections

### Socket Server (Node.js)
- **Runtime**: Node.js with TypeScript
- **Framework**: Socket.io for real-time communication
- **Database**: Prisma ORM with PostgreSQL
- **Validation**: Zod schemas for real-time event validation
- **Authentication**: JWT middleware for socket connections

## Prerequisites

Before running the application, ensure you have the following installed:

- Python 3.12+ with venv support
- Node.js 18+ and npm
- PostgreSQL 12+
- Redis 6+

## Environment Setup

### 1. Database Setup

Create a PostgreSQL database:
```sql
CREATE DATABASE chatapp;
CREATE USER postgres WITH PASSWORD 'password';
GRANT ALL PRIVILEGES ON DATABASE chatapp TO postgres;
```

### 2. Redis Setup

Start Redis server:
```bash
redis-server
```

### 3. Django Backend Setup

```bash
# Navigate to project root
cd /path/to/chatapp

# Activate virtual environment
source venv/bin/activate

# Navigate to backend
cd backend

# Run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser (optional)
python manage.py createsuperuser

# Start Django server
python manage.py runserver 8000
```

### 4. Socket Server Setup

```bash
# Navigate to socket server
cd socket-server

# Generate Prisma client
npm run prisma:generate

# Run database migrations
npm run prisma:migrate

# Start development server
npm run dev
```

### 5. React Frontend Setup

```bash
# Navigate to frontend
cd frontend

# Install dependencies
npm install

# Start Vite development server
npm run dev
```

The frontend now uses Vite for faster development and includes:
- Login and Registration pages with form validation
- Protected routes and authentication flow
- Tailwind CSS for styling
- Complete integration with the Django authentication API

## Environment Files

### Backend (.env)
```
DB_NAME=chatapp
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432
JWT_SECRET=your-jwt-secret-key
REDIS_URL=redis://localhost:6379
DEBUG=True
SECRET_KEY=django-insecure-eznvlbp*l!4@0q)u=e5w^0%=rc^c=piloz$r=2h@j5stnzhzyi
```

### Socket Server (.env)
```
DATABASE_URL="postgresql://postgres:password@localhost:5432/chatapp"
JWT_SECRET="your-jwt-secret-key"
REDIS_URL="redis://localhost:6379"
PORT=3001
```

## API Endpoints

### Authentication
- `POST /api/auth/register/` - User registration
- `POST /api/auth/login/` - User login

## Socket Events

### Client to Server
- `join_conversation` - Join a conversation room
- `leave_conversation` - Leave a conversation room
- `send_message` - Send a message
- `typing` - Send typing indicator
- `create_conversation` - Create new conversation

### Server to Client
- `new_message` - Receive new message
- `user_typing` - Receive typing indicator
- `new_conversation` - Receive new conversation
- `joined_conversation` - Confirmation of joining room
- `left_conversation` - Confirmation of leaving room
- `error` - Error messages

## Development URLs

- Django Backend: http://localhost:8000
- React Frontend: http://localhost:3000
- Socket Server: http://localhost:3001
- Django Admin: http://localhost:8000/admin

## Next Steps

The foundation is now complete. You can proceed with Phase 2 implementation which should include:

1. Frontend authentication pages
2. Chat interface components
3. Real-time messaging integration
4. User management features
5. Conversation management

## Troubleshooting

### Common Issues

1. **Database Connection Error**: Ensure PostgreSQL is running and credentials are correct
2. **JWT Token Issues**: Make sure JWT_SECRET is the same in both backend and socket server
3. **CORS Issues**: Verify CORS settings allow localhost:3000
4. **Port Conflicts**: Ensure ports 3000, 3001, and 8000 are available

### Logs

- Django logs: Check terminal running `python manage.py runserver`
- Socket server logs: Check terminal running `npm run dev`
- React logs: Check browser console and terminal running `npm start`
