# WebRTC Real Mode Implementation

## Overview

This document describes the implementation of real WebRTC functionality, replacing the mock implementations with actual audio and video calling capabilities using the laptop's built-in microphone and camera hardware.

## Changes Made

### 1. Mock Mode Control

**Before**: Mock mode was automatically enabled in test environments and development
**After**: Mock mode is only enabled explicitly via environment variables or URL parameters

- Removed automatic mock mode enablement for test environments
- Added explicit environment variable control (`VITE_WEBRTC_MOCK_MODE`)
- Created production environment configuration

### 2. Media Permission Handling

**Before**: Permissions were requested immediately when checking availability
**After**: Separated device enumeration from permission requests

- `checkMediaAvailability()`: Checks device availability without requesting permissions
- `checkMediaPermissions()`: Requests actual permissions when needed
- Permissions are only requested when calls are initiated

### 3. Real Media Stream Implementation

**Before**: Mock streams with canvas-generated content
**After**: Real media streams from hardware devices

- Proper `getUserMedia()` constraints for audio and video
- Enhanced audio settings (echo cancellation, noise suppression, auto gain control)
- Optimized video settings (640x480, 30fps)
- Proper error handling for permission denials

### 4. UI Updates

**Before**: Buttons always enabled regardless of device availability
**After**: Buttons reflect actual device availability

- Audio call button disabled if no microphone available
- Video call button disabled if no camera or microphone available
- Loading states while checking device availability
- Error indicators for device issues

## Environment Configuration

### Development Mode (Real WebRTC)
```bash
npm run dev
```

### Development Mode (Mock WebRTC for Testing)
```bash
npm run dev:mock
```

### Production Build
```bash
npm run build:prod
```

## Environment Variables

Create a `.env` file in the frontend directory:

```env
# WebRTC Configuration
VITE_WEBRTC_MOCK_MODE=false  # Set to true only for testing

# API Configuration
VITE_API_BASE_URL=http://localhost:6000
VITE_SOCKET_URL=http://localhost:7000
```

## Testing Real WebRTC

### Browser Console Test
1. Open the application in your browser
2. Open Developer Tools (F12)
3. Go to Console tab
4. Run: `testRealWebRTC()`

This will test:
- WebRTC API support
- Media devices enumeration
- Audio permission request
- Video permission request
- Combined audio/video permissions
- RTCPeerConnection creation

### Manual Testing
1. Start the application: `npm run dev`
2. Navigate to a conversation
3. Click the audio or video call button
4. Browser should prompt for microphone/camera permissions
5. Grant permissions to test real media access

## Permission Flow

### Audio Calls
1. User clicks audio call button
2. System checks if microphone devices are available
3. If available, requests microphone permission
4. Initiates call with real audio stream

### Video Calls
1. User clicks video call button
2. System checks if camera and microphone devices are available
3. If available, requests camera and microphone permissions
4. Initiates call with real audio and video streams

## Error Handling

### Common Errors
- `NotAllowedError`: User denied permission
- `NotFoundError`: No devices found
- `NotReadableError`: Device already in use

### UI Feedback
- Buttons disabled when devices unavailable
- Tooltip messages explain why buttons are disabled
- Error indicators show device issues

## Browser Compatibility

### Supported Browsers
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### Required Features
- WebRTC API (`RTCPeerConnection`)
- Media Devices API (`navigator.mediaDevices.getUserMedia`)
- Modern JavaScript (ES6+)

## Security Considerations

### HTTPS Requirement
- WebRTC requires HTTPS in production
- Local development (localhost) works with HTTP
- Self-signed certificates acceptable for testing

### Permission Persistence
- Browser remembers permission choices
- Users can revoke permissions in browser settings
- Application handles permission state changes

## Troubleshooting

### No Audio/Video Devices
- Check if devices are connected and working
- Verify browser permissions
- Test with other applications (e.g., browser's camera test)

### Permission Denied
- Check browser permission settings
- Clear site data and try again
- Ensure HTTPS in production

### WebRTC Connection Issues
- Check STUN/TURN server configuration
- Verify network connectivity
- Test with different networks (firewall issues)

## Future Enhancements

### Planned Features
- Device selection (multiple cameras/microphones)
- Quality settings (resolution, bitrate)
- Screen sharing capability
- Recording functionality

### Performance Optimizations
- Adaptive bitrate based on connection quality
- Automatic fallback to audio-only for poor connections
- Background noise suppression improvements
