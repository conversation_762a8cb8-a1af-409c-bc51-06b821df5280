// frontend/src/test/mocks/data.ts
export const mockUsers = [
  {
    id: 'user-1',
    username: 'testuser1',
    first_name: 'Test',
    last_name: 'User',
    email: '<EMAIL>',
    profile_picture: null,
    is_verified: true,
    last_seen: new Date().toISOString(),
    created_at: new Date().toISOString()
  },
  {
    id: 'user-2',
    username: 'testuser2',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    profile_picture: null,
    is_verified: true,
    last_seen: new Date().toISOString(),
    created_at: new Date().toISOString()
  },
  {
    id: 'user-3',
    username: 'testuser3',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    email: '<EMAIL>',
    profile_picture: null,
    is_verified: true,
    last_seen: new Date().toISOString(),
    created_at: new Date().toISOString()
  }
]

export const mockConversations = [
  {
    id: 'conv-1',
    type: 'DIRECT',
    name: null,
    participants: [mockUsers[0], mockUsers[1]],
    lastMessage: {
      id: 'msg-1',
      content: 'Hello there!',
      sender: { username: 'testuser2' },
      createdAt: new Date().toISOString()
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'conv-2',
    type: 'GROUP',
    name: 'Test Group',
    participants: [mockUsers[0], mockUsers[1], mockUsers[2]],
    lastMessage: {
      id: 'msg-2',
      content: 'Group message',
      sender: { username: 'testuser3' },
      createdAt: new Date().toISOString()
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export const mockMessages = [
  {
    id: 'msg-1',
    conversation_id: 'conv-1',
    conversationId: 'conv-1',
    sender: mockUsers[1],
    content: 'Hello there!',
    message_type: 'TEXT',
    messageType: 'TEXT',
    created_at: new Date(Date.now() - 60000).toISOString(),
    createdAt: new Date(Date.now() - 60000).toISOString(),
    updated_at: new Date(Date.now() - 60000).toISOString(),
    updatedAt: new Date(Date.now() - 60000).toISOString()
  },
  {
    id: 'msg-2',
    conversation_id: 'conv-1',
    conversationId: 'conv-1',
    sender: mockUsers[0],
    content: 'Hi! How are you?',
    message_type: 'TEXT',
    messageType: 'TEXT',
    created_at: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'msg-3',
    conversation_id: 'conv-2',
    conversationId: 'conv-2',
    sender: mockUsers[2],
    content: 'Group message',
    message_type: 'TEXT',
    messageType: 'TEXT',
    created_at: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]
