// frontend/src/utils/webrtc.ts
export interface CallQualityMetrics {
  packetLoss?: number;
  jitter?: number;
  roundTripTime?: number;
  bandwidthUpload?: number;
  bandwidthDownload?: number;
  audioLevel?: number;
  audioQualityScore?: number;
  videoResolution?: string;
  videoFramerate?: number;
  videoQualityScore?: number;
}

export interface WebRTCManagerEvents {
  onRemoteStream?: (stream: MediaStream) => void;
  onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
  onQualityUpdate?: (metrics: CallQualityMetrics) => void;
  onError?: (error: Error) => void;
}

export class WebRTCManager {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private socket: any;
  private callId: string | null = null;
  private qualityMonitorInterval: NodeJS.Timeout | null = null;
  private mockMode: boolean = false;
  private mockStreams: { local?: MediaStream; remote?: MediaStream } = {};

  // STUN/TURN server configuration
  private readonly rtcConfiguration: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' },
      // Add TURN servers for production
      // {
      //   urls: 'turn:your-turn-server.com:3478',
      //   username: 'username',
      //   credential: 'password'
      // }
    ],
    iceCandidatePoolSize: 10,
  };

  // Event handlers
  public events: WebRTCManagerEvents = {};

  constructor(socket: any, options?: { mockMode?: boolean }) {
    this.socket = socket;
    this.mockMode = options?.mockMode || false;
    this.setupSocketListeners();
    
    if (this.mockMode) {
      console.log('🎭 WebRTC Manager running in MOCK MODE for testing');
    }
  }

  // Create mock media streams for testing
  private createMockStream(isVideo: boolean = false): MediaStream {
    console.log(`🎭 Creating mock ${isVideo ? 'video' : 'audio'} stream`);
    
    // Create a canvas for mock video
    if (isVideo) {
      const canvas = document.createElement('canvas');
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext('2d')!;
      
      // Draw a simple pattern
      ctx.fillStyle = '#2563eb';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = 'white';
      ctx.font = '24px Arial';
      ctx.fillText('Mock Video Stream', canvas.width / 2 - 80, canvas.height / 2);
      ctx.fillText('Testing Mode', canvas.width / 2 - 60, canvas.height / 2 + 30);
      
      // Get stream from canvas
      const stream = canvas.captureStream(30);
      
      // Add mock audio track
      if (this.mockMode) {
        this.addMockAudioTrack(stream);
      }
      
      return stream;
    } else {
      // Create mock audio-only stream
      const stream = new MediaStream();
      this.addMockAudioTrack(stream);
      return stream;
    }
  }

  private addMockAudioTrack(stream: MediaStream) {
    // Create a silent audio track
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const destination = audioContext.createMediaStreamDestination();
    const gainNode = audioContext.createGain();
    
    // Set gain to very low to create silent audio
    gainNode.gain.value = 0.01;
    
    oscillator.connect(gainNode);
    gainNode.connect(destination);
    oscillator.frequency.value = 440; // A4 note
    oscillator.start();
    
    // Add the audio track to the stream
    destination.stream.getAudioTracks().forEach(track => {
      stream.addTrack(track);
    });
  }

  // Check if media devices are available (without requesting permissions)
  async checkMediaAvailability(requireVideo: boolean = false): Promise<{
    audio: boolean;
    video: boolean;
    error?: string;
  }> {
    // In mock mode, always return available
    if (this.mockMode) {
      console.log('🎭 Mock mode: Media availability check - returning available');
      return {
        audio: true,
        video: true,
      };
    }

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        return {
          audio: false,
          video: false,
          error: 'Media devices not supported in this browser'
        };
      }

      // Check device availability without requesting permissions
      const devices = await navigator.mediaDevices.enumerateDevices();
      const hasAudio = devices.some(device => device.kind === 'audioinput');
      const hasVideo = devices.some(device => device.kind === 'videoinput');

      // Return device availability without testing permissions
      // Permissions will be requested when the call is actually initiated
      return {
        audio: hasAudio,
        video: hasVideo,
      };
    } catch (error) {
      return {
        audio: false,
        video: false,
        error: 'Failed to check media availability'
      };
    }
  }

  // Check if media permissions are granted (this will request permissions)
  async checkMediaPermissions(requireVideo: boolean = false): Promise<{
    audio: boolean;
    video: boolean;
    error?: string;
  }> {
    // In mock mode, always return granted
    if (this.mockMode) {
      console.log('🎭 Mock mode: Media permissions check - returning granted');
      return {
        audio: true,
        video: true,
      };
    }

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        return {
          audio: false,
          video: false,
          error: 'Media devices not supported in this browser'
        };
      }

      const devices = await navigator.mediaDevices.enumerateDevices();
      const hasAudio = devices.some(device => device.kind === 'audioinput');
      const hasVideo = devices.some(device => device.kind === 'videoinput');

      // Request permissions by attempting to get media stream
      try {
        const testStream = await navigator.mediaDevices.getUserMedia({
          audio: hasAudio,
          video: requireVideo && hasVideo
        });

        // Stop the test stream immediately
        testStream.getTracks().forEach(track => track.stop());

        return {
          audio: hasAudio,
          video: hasVideo,
        };
      } catch (permissionError) {
        return {
          audio: hasAudio,
          video: hasVideo,
          error: this.getPermissionErrorMessage(permissionError as Error)
        };
      }
    } catch (error) {
      return {
        audio: false,
        video: false,
        error: 'Failed to check media permissions'
      };
    }
  }

  private getPermissionErrorMessage(error: Error): string {
    if (error.name === 'NotAllowedError') {
      return 'Camera/microphone access denied. Please allow permissions and try again.';
    } else if (error.name === 'NotFoundError') {
      return 'No camera or microphone found. Please check your devices.';
    } else if (error.name === 'NotReadableError') {
      return 'Camera or microphone is already in use by another application.';
    }
    return error.message || 'Permission check failed';
  }

  private setupSocketListeners() {
    // Add logging for socket events
    this.socket.on('webrtc_offer', (data: any) => {
      console.log('🔌 Socket event received: webrtc_offer', data);
      this.handleOffer(data);
    });
    
    this.socket.on('webrtc_answer', (data: any) => {
      console.log('🔌 Socket event received: webrtc_answer', data);
      this.handleAnswer(data);
    });
    
    this.socket.on('webrtc_ice_candidate', (data: any) => {
      console.log('🔌 Socket event received: webrtc_ice_candidate', data);
      this.handleIceCandidate(data);
    });
  }

  async initializeCall(callId: string, isVideo: boolean = false): Promise<void> {
    console.log(`[DEBUG] WebRTC initializeCall - Setting callId: ${callId} (type: ${typeof callId})`);
    
    // Validate UUID format (basic check)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(callId) && callId !== 'pending') {
      console.warn(`[DEBUG] WebRTC - Invalid UUID format for callId: ${callId}`);
    }
    
    this.callId = callId;

    try {
      // In mock mode, use mock streams instead of real media devices
      if (this.mockMode) {
        console.log('🎭 Mock mode: Creating mock media streams');
        this.localStream = this.createMockStream(isVideo);
        this.mockStreams.local = this.localStream;
        
        // Simulate remote stream after a delay
        setTimeout(() => {
          this.mockStreams.remote = this.createMockStream(isVideo);
          this.events.onRemoteStream?.(this.mockStreams.remote);
        }, 2000);
        
        // Simulate connection state change to 'connected' in mock mode
        setTimeout(() => {
          console.log('🎭 Mock mode: Simulating connection state change to connected');
          this.events.onConnectionStateChange?.('connected');
        }, 3000);
      } else {
        // Real media device handling
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error('Media devices not supported in this browser');
        }

        console.log(`🎥 Requesting real media access - Audio: true, Video: ${isVideo}`);

        // Check available devices before requesting access
        const devices = await navigator.mediaDevices.enumerateDevices();
        const hasAudio = devices.some(device => device.kind === 'audioinput');
        const hasVideo = devices.some(device => device.kind === 'videoinput');

        if (!hasAudio) {
          throw new Error('No audio input devices found. Please check your microphone.');
        }

        if (isVideo && !hasVideo) {
          console.warn('No video input devices found, falling back to audio-only call');
          isVideo = false;
        }

        // Get user media with proper constraints
        const constraints: MediaStreamConstraints = {
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          },
          video: isVideo && hasVideo ? {
            width: { ideal: 640 },
            height: { ideal: 480 },
            frameRate: { ideal: 30 }
          } : false
        };

        console.log('🎥 Requesting media with constraints:', constraints);
        this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('🎥 Successfully obtained media stream:', {
          audioTracks: this.localStream.getAudioTracks().length,
          videoTracks: this.localStream.getVideoTracks().length
        });
      }

      // Create peer connection
      this.peerConnection = new RTCPeerConnection(this.rtcConfiguration);

      // Add local stream to peer connection
      this.localStream.getTracks().forEach(track => {
        if (this.peerConnection && this.localStream) {
          this.peerConnection.addTrack(track, this.localStream);
        }
      });

      // Handle remote stream
      this.peerConnection.ontrack = (event) => {
        this.remoteStream = event.streams[0];
        this.events.onRemoteStream?.(this.remoteStream);
      };

      // Handle ICE candidates
      this.peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          // Validate callId before emitting
          if (!this.callId || this.callId === 'pending') {
            console.warn('[DEBUG] WebRTC - Skipping ICE candidate emission - invalid callId:', this.callId);
            return;
          }
          
          const data = {
            callId: this.callId,
            candidate: event.candidate
          };
          console.log('[DEBUG] Sending ICE candidate with callId:', this.callId, data);
          this.socket.emit('webrtc_ice_candidate', data);
        }
      };

      // Handle connection state changes
      this.peerConnection.onconnectionstatechange = () => {
        const state = this.peerConnection?.connectionState;
        this.events.onConnectionStateChange?.(state || 'closed');

        if (state === 'connected') {
          this.startQualityMonitoring();
        } else if (state === 'disconnected' || state === 'failed' || state === 'closed') {
          this.stopQualityMonitoring();
        }
      };

      // Handle ICE connection state changes
      this.peerConnection.oniceconnectionstatechange = () => {
        const state = this.peerConnection?.iceConnectionState;
        console.log('ICE connection state:', state);

        if (state === 'failed') {
          this.events.onError?.(new Error('ICE connection failed'));
        }
      };

    } catch (error) {
      console.error('Failed to initialize call:', error);
      
      // Provide specific error messages for common issues
      let errorMessage = 'Failed to initialize call';
      
      if (error instanceof Error) {
        if (error.name === 'NotFoundError') {
          errorMessage = 'No camera or microphone found. Please check your devices.';
        } else if (error.name === 'NotAllowedError') {
          errorMessage = 'Camera/microphone access denied. Please allow permissions.';
        } else if (error.name === 'NotReadableError') {
          errorMessage = 'Camera or microphone is already in use by another application.';
        } else if (error.name === 'OverconstrainedError') {
          errorMessage = 'Camera or microphone constraints cannot be satisfied.';
        } else if (error.name === 'AbortError') {
          errorMessage = 'Media request was aborted.';
        } else if (error.name === 'TypeError') {
          errorMessage = 'Media devices not supported in this browser.';
        } else {
          errorMessage = error.message || 'Unknown error occurred';
        }
      }
      
      const enhancedError = new Error(errorMessage);
      this.events.onError?.(enhancedError);
      throw enhancedError;
    }
  }

  async createOffer(): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      const offer = await this.peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: true
      });

      await this.peerConnection.setLocalDescription(offer);

      // Validate callId before emitting
      if (!this.callId || this.callId === 'pending') {
        console.warn('[DEBUG] WebRTC - Skipping offer emission - invalid callId:', this.callId);
        return;
      }
      
      const offerData = {
        callId: this.callId,
        offer: offer
      };
      console.log('[DEBUG] Sending WebRTC offer with callId:', this.callId, offerData);
      this.socket.emit('webrtc_offer', offerData);

    } catch (error) {
      console.error('Failed to create offer:', error);
      this.events.onError?.(error as Error);
      throw error;
    }
  }

  private async handleOffer(data: any): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(data.offer);

      const answer = await this.peerConnection.createAnswer();
      await this.peerConnection.setLocalDescription(answer);

      const answerData = {
        callId: data.callId,
        answer: answer
      };
      console.log('🔌 Socket event emitted: webrtc_answer', answerData);
      this.socket.emit('webrtc_answer', answerData);

    } catch (error) {
      console.error('Failed to handle offer:', error);
      this.events.onError?.(error as Error);
      throw error;
    }
  }

  private async handleAnswer(data: any): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('Peer connection not initialized');
    }

    try {
      await this.peerConnection.setRemoteDescription(data.answer);
    } catch (error) {
      console.error('Failed to handle answer:', error);
      this.events.onError?.(error as Error);
      throw error;
    }
  }

  private async handleIceCandidate(data: any): Promise<void> {
    if (!this.peerConnection) {
      return;
    }

    try {
      await this.peerConnection.addIceCandidate(data.candidate);
    } catch (error) {
      console.error('Failed to add ICE candidate:', error);
      // Don't throw here as ICE candidates can fail without breaking the call
    }
  }

  toggleAudio(): boolean {
    if (!this.localStream) return false;

    const audioTrack = this.localStream.getAudioTracks()[0];
    if (audioTrack) {
      audioTrack.enabled = !audioTrack.enabled;

      this.socket.emit('toggle_audio', {
        callId: this.callId,
        enabled: audioTrack.enabled
      });

      return audioTrack.enabled;
    }
    return false;
  }

  toggleVideo(): boolean {
    if (!this.localStream) return false;

    const videoTrack = this.localStream.getVideoTracks()[0];
    if (videoTrack) {
      videoTrack.enabled = !videoTrack.enabled;

      this.socket.emit('toggle_video', {
        callId: this.callId,
        enabled: videoTrack.enabled
      });

      return videoTrack.enabled;
    }
    return false;
  }

  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  getRemoteStream(): MediaStream | null {
    return this.remoteStream;
  }

  isAudioEnabled(): boolean {
    if (!this.localStream) return false;
    const audioTrack = this.localStream.getAudioTracks()[0];
    return audioTrack ? audioTrack.enabled : false;
  }

  isVideoEnabled(): boolean {
    if (!this.localStream) return false;
    const videoTrack = this.localStream.getVideoTracks()[0];
    return videoTrack ? videoTrack.enabled : false;
  }

  private startQualityMonitoring(): void {
    if (!this.peerConnection || this.qualityMonitorInterval) return;

    this.qualityMonitorInterval = setInterval(async () => {
      if (!this.peerConnection || this.peerConnection.connectionState !== 'connected') {
        this.stopQualityMonitoring();
        return;
      }

      try {
        const stats = await this.peerConnection.getStats();
        const metrics = this.parseStats(stats);

        this.socket.emit('call_quality_report', {
          callId: this.callId,
          metrics
        });

        this.events.onQualityUpdate?.(metrics);

      } catch (error) {
        console.error('Failed to get call stats:', error);
      }
    }, 5000); // Report every 5 seconds
  }

  private stopQualityMonitoring(): void {
    if (this.qualityMonitorInterval) {
      clearInterval(this.qualityMonitorInterval);
      this.qualityMonitorInterval = null;
    }
  }

  private parseStats(stats: RTCStatsReport): CallQualityMetrics {
    const metrics: CallQualityMetrics = {};

    stats.forEach((report) => {
      if (report.type === 'inbound-rtp' && report.mediaType === 'audio') {
        metrics.packetLoss = report.packetsLost || 0;
        metrics.jitter = report.jitter ? report.jitter * 1000 : undefined; // Convert to ms
      } else if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
        metrics.packetLoss = report.packetsLost || 0;
        if (report.frameWidth && report.frameHeight) {
          metrics.videoResolution = `${report.frameWidth}x${report.frameHeight}`;
        }
        metrics.videoFramerate = report.framesPerSecond;
      } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
        metrics.roundTripTime = report.currentRoundTripTime ? report.currentRoundTripTime * 1000 : undefined; // Convert to ms
      } else if (report.type === 'outbound-rtp') {
        // Get bandwidth information
        if (report.bytesSent && report.timestamp) {
          // This is a simplified calculation - in practice you'd want to track over time
          metrics.bandwidthUpload = Math.round((report.bytesSent * 8) / 1000); // Convert to kbps
        }
      }
    });

    return metrics;
  }

  endCall(): void {
    console.log('📞 Ending call');
    
    this.stopQualityMonitoring();
    
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }
    
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    
    this.remoteStream = null;
    this.callId = null;
    this.mockStreams = {};
  }

  // Testing methods for simulating different scenarios
  setMockMode(enabled: boolean): void {
    this.mockMode = enabled;
    console.log(`🎭 Mock mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  isMockMode(): boolean {
    return this.mockMode;
  }

  // Simulate incoming call for testing
  simulateIncomingCall(callId: string, isVideo: boolean = false): void {
    if (!this.mockMode) {
      console.warn('simulateIncomingCall only works in mock mode');
      return;
    }

    console.log('🎭 Simulating incoming call:', { callId, isVideo });
    
    // Emit socket events that would normally come from the server
    setTimeout(() => {
      this.socket?.emit('call_incoming', {
        callId,
        isVideo,
        caller: { id: 'test-caller', username: 'Test Caller' }
      });
    }, 100);
  }

  // Simulate call events for testing
  simulateCallEvent(eventType: string, data: any = {}): void {
    if (!this.mockMode) {
      console.warn('simulateCallEvent only works in mock mode');
      return;
    }

    console.log(`🎭 Simulating call event: ${eventType}`, data);
    
    const eventData = {
      callId: this.callId || 'test-call-id',
      ...data
    };

    switch (eventType) {
      case 'call_accepted':
        this.socket?.emit('call_accepted', eventData);
        break;
      case 'call_rejected':
        this.socket?.emit('call_rejected', eventData);
        break;
      case 'call_ended':
        this.socket?.emit('call_ended', eventData);
        break;
      case 'call_failed':
        this.socket?.emit('call_failed', { ...eventData, error: data.error || 'Test error' });
        break;
      case 'webrtc_offer':
        this.socket?.emit('webrtc_offer', { ...eventData, offer: data.offer || 'mock-offer' });
        break;
      case 'webrtc_answer':
        this.socket?.emit('webrtc_answer', { ...eventData, answer: data.answer || 'mock-answer' });
        break;
      case 'webrtc_ice_candidate':
        this.socket?.emit('webrtc_ice_candidate', { 
          ...eventData, 
          candidate: data.candidate || { candidate: 'mock-candidate', sdpMid: '0', sdpMLineIndex: 0 }
        });
        break;
      default:
        console.warn(`Unknown event type: ${eventType}`);
    }
  }

  // Get current connection state for testing
  getConnectionState(): RTCPeerConnectionState | 'disconnected' {
    return this.peerConnection?.connectionState || 'disconnected';
  }

  // Force media availability state for testing
  async simulateMediaAvailability(audio: boolean, video: boolean): Promise<void> {
    if (!this.mockMode) {
      console.warn('simulateMediaAvailability only works in mock mode');
      return;
    }

    console.log(`🎭 Simulating media availability - Audio: ${audio}, Video: ${video}`);
    
    // Override the checkMediaAvailability method temporarily
    const originalCheck = this.checkMediaAvailability.bind(this);
    this.checkMediaAvailability = async () => ({ audio, video });
    
    // Restore original method after a delay
    setTimeout(() => {
      this.checkMediaAvailability = originalCheck;
    }, 5000);
  }
}