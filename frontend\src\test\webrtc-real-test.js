// Test script to verify real WebRTC functionality
// Run this in the browser console to test real media access

async function testRealWebRTC() {
  console.log('🎥 Testing Real WebRTC Functionality');
  console.log('=====================================');

  // Test 1: Check if WebRTC is supported
  console.log('\n1. Checking WebRTC Support...');
  if (!window.RTCPeerConnection) {
    console.error('❌ RTCPeerConnection not supported');
    return;
  }
  console.log('✅ RTCPeerConnection supported');

  // Test 2: Check media devices API
  console.log('\n2. Checking Media Devices API...');
  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    console.error('❌ Media Devices API not supported');
    return;
  }
  console.log('✅ Media Devices API supported');

  // Test 3: Enumerate devices (without permissions)
  console.log('\n3. Enumerating Media Devices...');
  try {
    const devices = await navigator.mediaDevices.enumerateDevices();
    const audioInputs = devices.filter(device => device.kind === 'audioinput');
    const videoInputs = devices.filter(device => device.kind === 'videoinput');
    
    console.log(`📱 Found ${audioInputs.length} audio input devices`);
    console.log(`📹 Found ${videoInputs.length} video input devices`);
    
    if (audioInputs.length === 0) {
      console.warn('⚠️ No audio input devices found');
    }
    if (videoInputs.length === 0) {
      console.warn('⚠️ No video input devices found');
    }
  } catch (error) {
    console.error('❌ Failed to enumerate devices:', error);
    return;
  }

  // Test 4: Request audio permission
  console.log('\n4. Testing Audio Permission...');
  try {
    const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    console.log('✅ Audio permission granted');
    console.log(`🎤 Audio tracks: ${audioStream.getAudioTracks().length}`);
    
    // Stop the stream
    audioStream.getTracks().forEach(track => track.stop());
    console.log('🛑 Audio stream stopped');
  } catch (error) {
    console.error('❌ Audio permission denied or failed:', error.message);
  }

  // Test 5: Request video permission
  console.log('\n5. Testing Video Permission...');
  try {
    const videoStream = await navigator.mediaDevices.getUserMedia({ 
      video: { width: 640, height: 480 } 
    });
    console.log('✅ Video permission granted');
    console.log(`📹 Video tracks: ${videoStream.getVideoTracks().length}`);
    
    // Stop the stream
    videoStream.getTracks().forEach(track => track.stop());
    console.log('🛑 Video stream stopped');
  } catch (error) {
    console.error('❌ Video permission denied or failed:', error.message);
  }

  // Test 6: Request both audio and video
  console.log('\n6. Testing Combined Audio/Video Permission...');
  try {
    const combinedStream = await navigator.mediaDevices.getUserMedia({ 
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      },
      video: { 
        width: { ideal: 640 }, 
        height: { ideal: 480 },
        frameRate: { ideal: 30 }
      } 
    });
    console.log('✅ Combined audio/video permission granted');
    console.log(`🎤 Audio tracks: ${combinedStream.getAudioTracks().length}`);
    console.log(`📹 Video tracks: ${combinedStream.getVideoTracks().length}`);
    
    // Test track properties
    const audioTrack = combinedStream.getAudioTracks()[0];
    const videoTrack = combinedStream.getVideoTracks()[0];
    
    if (audioTrack) {
      console.log(`🎤 Audio track: ${audioTrack.label}, enabled: ${audioTrack.enabled}`);
    }
    if (videoTrack) {
      console.log(`📹 Video track: ${videoTrack.label}, enabled: ${videoTrack.enabled}`);
    }
    
    // Stop the stream
    combinedStream.getTracks().forEach(track => track.stop());
    console.log('🛑 Combined stream stopped');
  } catch (error) {
    console.error('❌ Combined permission denied or failed:', error.message);
  }

  // Test 7: Create RTCPeerConnection
  console.log('\n7. Testing RTCPeerConnection Creation...');
  try {
    const pc = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    });
    console.log('✅ RTCPeerConnection created successfully');
    console.log(`🔗 Connection state: ${pc.connectionState}`);
    console.log(`🧊 ICE connection state: ${pc.iceConnectionState}`);
    
    pc.close();
    console.log('🛑 RTCPeerConnection closed');
  } catch (error) {
    console.error('❌ Failed to create RTCPeerConnection:', error);
  }

  console.log('\n🎉 WebRTC Real Mode Test Complete!');
  console.log('=====================================');
}

// Export for use in tests
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testRealWebRTC };
} else {
  // Make available globally in browser
  window.testRealWebRTC = testRealWebRTC;
}

console.log('🎥 Real WebRTC Test Script Loaded');
console.log('Run testRealWebRTC() to start testing');
