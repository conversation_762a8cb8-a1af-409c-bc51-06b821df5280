// frontend/src/components/Call/IncomingCallModal.tsx
import React, { useEffect, useState } from 'react';
import { Phone, PhoneOff, Video, User } from 'lucide-react';
import { useCalling } from '../../contexts/CallingContext';

interface IncomingCallModalProps {
  isOpen: boolean;
}

export const IncomingCallModal: React.FC<IncomingCallModalProps> = ({ isOpen }) => {
  const { activeCall, answerCall, declineCall } = useCalling();
  const [isRinging, setIsRinging] = useState(false);

  // Ringing animation
  useEffect(() => {
    if (isOpen && activeCall?.isIncoming) {
      const interval = setInterval(() => {
        setIsRinging(prev => !prev);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isOpen, activeCall?.isIncoming]);

  if (!isOpen || !activeCall?.isIncoming) {
    return null;
  }

  const handleAnswer = () => {
    answerCall();
  };

  const handleDecline = () => {
    declineCall();
  };

  const getCallTypeIcon = () => {
    return activeCall.type === 'video' ? (
      <Video className="w-8 h-8 text-green-600" />
    ) : (
      <Phone className="w-8 h-8 text-green-600" />
    );
  };

  const getDisplayName = () => {
    const { firstName, lastName, username } = activeCall.caller;
    if (firstName || lastName) {
      return `${firstName || ''} ${lastName || ''}`.trim();
    }
    return username;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" data-testid="incoming-call-modal">
      <div className="bg-white rounded-2xl p-8 max-w-sm w-full mx-4 text-center shadow-2xl">
        {/* Call type indicator */}
        <div className="flex justify-center mb-4">
          {getCallTypeIcon()}
        </div>

        {/* Caller info */}
        <div className="mb-6">
          <div className="w-24 h-24 mx-auto mb-4 relative">
            {activeCall.caller.profilePicture ? (
              <img
                src={activeCall.caller.profilePicture}
                alt={getDisplayName()}
                className={`w-full h-full rounded-full object-cover transition-transform duration-1000 ${
                  isRinging ? 'scale-110' : 'scale-100'
                }`}
              />
            ) : (
              <div
                className={`w-full h-full rounded-full bg-gray-300 flex items-center justify-center transition-transform duration-1000 ${
                  isRinging ? 'scale-110' : 'scale-100'
                }`}
              >
                <User className="w-12 h-12 text-gray-600" />
              </div>
            )}
            
            {/* Ringing indicator */}
            {isRinging && (
              <div className="absolute inset-0 rounded-full border-4 border-green-500 animate-ping" />
            )}
          </div>

          <h2 className="text-xl font-semibold text-gray-900 mb-1">
            {getDisplayName()}
          </h2>
          <p className="text-gray-600">
            Incoming {activeCall.type} call...
          </p>
        </div>

        {/* Call actions */}
        <div className="flex justify-center space-x-8">
          {/* Decline button */}
          <button
            onClick={handleDecline}
            className="w-16 h-16 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors duration-200 shadow-lg"
            title="Decline call"
            data-testid="decline-call-button"
          >
            <PhoneOff className="w-8 h-8 text-white" />
          </button>

          {/* Answer button */}
          <button
            onClick={handleAnswer}
            className="w-16 h-16 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center transition-colors duration-200 shadow-lg"
            title="Answer call"
            data-testid="answer-call-button"
          >
            <Phone className="w-8 h-8 text-white" />
          </button>
        </div>

        {/* Call status */}
        <div className="mt-4 text-sm text-gray-500">
          {activeCall.status === 'ringing' && 'Ringing...'}
          {activeCall.status === 'connecting' && 'Connecting...'}
        </div>
      </div>
    </div>
  );
};
