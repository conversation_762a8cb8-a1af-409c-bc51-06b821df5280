// frontend/src/components/Chat/__tests__/ChatRoom.test.tsx
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen } from '@testing-library/react'
import { Provider } from 'react-redux'
import ChatRoom from '../ChatRoom'
import { renderWithStore } from '../../../test/utils'

// Mock the contexts
const mockAuthContext = {
  user: {
    id: 'user-1',
    username: 'john_doe',
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>'
  },
  isAuthenticated: true,
  login: vi.fn(),
  logout: vi.fn(),
  loading: false
}

const mockSocketContext = {
  joinConversation: vi.fn(),
  isConnected: true
}

vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext
}))

vi.mock('../../../contexts/SocketContext', () => ({
  useSocket: () => mockSocketContext
}))

// Mock the messageSlice
vi.mock('../../../store/slices/messageSlice', () => ({
  default: (state = {}) => state
}))

// Mock useDispatch
const mockDispatch = vi.fn()
vi.mock('react-redux', async () => {
  const actual = await vi.importActual('react-redux')
  return {
    ...actual,
    useDispatch: () => mockDispatch
  }
})

// Mock the child components
vi.mock('../MessageList', () => ({
  default: ({ conversationId, currentUserId }: any) => (
    <div data-testid="message-list">
      MessageList - {conversationId} - {currentUserId}
    </div>
  )
}))

vi.mock('../MessageInput', () => ({
  default: ({ conversationId, disabled }: any) => (
    <div data-testid="message-input">
      MessageInput - {conversationId} - {disabled ? 'disabled' : 'enabled'}
    </div>
  )
}))



describe('ChatRoom', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockDispatch.mockClear()
    mockSocketContext.isConnected = true
    mockAuthContext.user = {
      id: 'user-1',
      username: 'john_doe',
      first_name: 'John',
      last_name: 'Doe',
      email: '<EMAIL>'
    }
  })

  it('renders chat room components correctly', () => {
    renderWithStore(<ChatRoom conversationId="conv-1" />)

    expect(screen.getByTestId('message-list')).toBeInTheDocument()
    expect(screen.getByTestId('message-input')).toBeInTheDocument()
    expect(screen.getByText('MessageList - conv-1 - user-1')).toBeInTheDocument()
    expect(screen.getByText('MessageInput - conv-1 - enabled')).toBeInTheDocument()
  })

  it('fetches messages and joins conversation on mount', () => {
    renderWithStore(<ChatRoom conversationId="conv-1" />)

    // Check that joinConversation was called (message fetching is now handled by RTK Query)
    expect(mockSocketContext.joinConversation).toHaveBeenCalledWith('conv-1')
  })

  it('does not join conversation when not connected', () => {
    mockSocketContext.isConnected = false

    renderWithStore(<ChatRoom conversationId="conv-1" />)

    // Should not join conversation when disconnected
    expect(mockSocketContext.joinConversation).not.toHaveBeenCalled()
  })

  it('shows connection status when disconnected', () => {
    mockSocketContext.isConnected = false

    renderWithStore(<ChatRoom conversationId="conv-1" />)

    expect(screen.getByText('Reconnecting...')).toBeInTheDocument()
    expect(screen.getByTestId('message-input')).toHaveTextContent('disabled')

    // Should show the connection indicator with proper styling
    const connectionStatus = screen.getByText('Reconnecting...')
    const statusContainer = connectionStatus.closest('.bg-yellow-50')
    expect(statusContainer).toBeInTheDocument()
  })

  it('hides connection status when connected', () => {
    mockSocketContext.isConnected = true

    renderWithStore(<ChatRoom conversationId="conv-1" />)

    expect(screen.queryByText('Reconnecting...')).not.toBeInTheDocument()
    expect(screen.getByTestId('message-input')).toHaveTextContent('enabled')
  })

  it('shows login prompt when user is not authenticated', () => {
    mockAuthContext.user = null

    renderWithStore(<ChatRoom conversationId="conv-1" />)

    expect(screen.getByText('Please log in to access chat')).toBeInTheDocument()
    expect(screen.queryByTestId('message-list')).not.toBeInTheDocument()
    expect(screen.queryByTestId('message-input')).not.toBeInTheDocument()
  })

  it('handles empty conversation ID gracefully', () => {
    renderWithStore(<ChatRoom conversationId="" />)

    // Should not call joinConversation with empty ID
    expect(mockSocketContext.joinConversation).not.toHaveBeenCalled()

    // But should still render components
    expect(screen.getByTestId('message-list')).toBeInTheDocument()
    expect(screen.getByTestId('message-input')).toBeInTheDocument()
  })

  it('updates when conversation ID changes', () => {
    const { rerender, store } = renderWithStore(<ChatRoom conversationId="conv-1" />)

    // Verify initial calls
    expect(mockSocketContext.joinConversation).toHaveBeenCalledWith('conv-1')

    // Clear mocks and change conversation ID
    mockSocketContext.joinConversation.mockClear()

    // Need to wrap the rerendered component in Provider manually
    rerender(
      <Provider store={store}>
        <ChatRoom conversationId="conv-2" />
      </Provider>
    )

    // Should join new conversation
    expect(mockSocketContext.joinConversation).toHaveBeenCalledWith('conv-2')
  })

  it('has proper layout structure', () => {
    renderWithStore(<ChatRoom conversationId="conv-1" />)

    const container = screen.getByTestId('message-list').closest('.flex.flex-col.h-full')
    expect(container).toBeInTheDocument()
  })

  it('passes correct props to MessageList', () => {
    renderWithStore(<ChatRoom conversationId="conv-1" />)

    const messageList = screen.getByTestId('message-list')
    expect(messageList).toHaveTextContent('MessageList - conv-1 - user-1')
  })

  it('passes correct props to MessageInput', () => {
    renderWithStore(<ChatRoom conversationId="conv-1" />)

    const messageInput = screen.getByTestId('message-input')
    expect(messageInput).toHaveTextContent('MessageInput - conv-1 - enabled')
  })

  it('disables MessageInput when not connected', () => {
    mockSocketContext.isConnected = false

    renderWithStore(<ChatRoom conversationId="conv-1" />)

    const messageInput = screen.getByTestId('message-input')
    expect(messageInput).toHaveTextContent('disabled')
  })
})
