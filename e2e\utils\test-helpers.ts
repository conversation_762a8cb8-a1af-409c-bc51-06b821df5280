import { <PERSON>, BrowserContext, expect } from '@playwright/test';
import { TestDataManager, TestUser } from './test-data-manager';

export class TestHelpers {
  static async authenticateUser(context: BrowserContext, userKey: string = 'primary'): Promise<void> {
    const testDataManager = new TestDataManager();
    const testUser = testDataManager.getTestUser(userKey);
    
    const page = await context.newPage();
    
    try {
      await page.goto('/login');
      await page.fill('[data-testid="email-input"], input[type="email"]', testUser.email);
      await page.fill('[data-testid="password-input"], input[type="password"]', testUser.password);
      await page.click('[data-testid="login-button"], button[type="submit"]');
      
      // Wait for successful login
      await page.waitForURL('**/dashboard', { timeout: 10000 });
      
      // Save authentication state
      await context.storageState({ path: `e2e/fixtures/auth-state-${userKey}.json` });
    } finally {
      await page.close();
    }
  }

  static async createMultipleAuthStates(context: BrowserContext, userKeys: string[]): Promise<void> {
    for (const userKey of userKeys) {
      await this.authenticateUser(context, userKey);
    }
  }

  static async waitForSocketConnection(page: Page, timeout: number = 10000): Promise<void> {
    // Wait for socket connection indicator
    const connectionStatus = page.locator('[data-testid="connection-status"], .connection-status');
    await expect(connectionStatus).toContainText(/connected|online/i, { timeout });
  }

  static async waitForElement(page: Page, selector: string, timeout: number = 5000): Promise<void> {
    await expect(page.locator(selector)).toBeVisible({ timeout });
  }

  static async waitForText(page: Page, text: string, timeout: number = 5000): Promise<void> {
    await expect(page.locator(`text=${text}`)).toBeVisible({ timeout });
  }

  static async waitForNetworkIdle(page: Page, timeout: number = 5000): Promise<void> {
    await page.waitForLoadState('networkidle', { timeout });
  }

  static async simulateNetworkFailure(page: Page): Promise<void> {
    await page.route('**/*', route => route.abort());
  }

  static async restoreNetwork(page: Page): Promise<void> {
    await page.unroute('**/*');
  }

  static async simulateSlowNetwork(page: Page, delay: number = 1000): Promise<void> {
    await page.route('**/*', async route => {
      await new Promise(resolve => setTimeout(resolve, delay));
      await route.continue();
    });
  }

  static async interceptAPICall(page: Page, url: string, response: any): Promise<void> {
    await page.route(url, route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(response)
      });
    });
  }

  static async interceptAPIError(page: Page, url: string, status: number = 500): Promise<void> {
    await page.route(url, route => {
      route.fulfill({
        status,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Simulated error' })
      });
    });
  }

  static async takeScreenshot(page: Page, name: string): Promise<void> {
    await page.screenshot({ 
      path: `e2e/test-results/screenshots/${name}-${Date.now()}.png`,
      fullPage: true 
    });
  }

  static async recordVideo(page: Page, name: string): Promise<void> {
    // Video recording is handled by Playwright configuration
    // This is a placeholder for custom video handling if needed
  }

  static async generateTestData(): Promise<{
    users: TestUser[];
    conversations: any[];
    messages: any[];
  }> {
    const testDataManager = new TestDataManager();
    
    return {
      users: Array.from(testDataManager.getAllTestUsers().values()),
      conversations: Array.from(testDataManager.getAllTestConversations().values()),
      messages: [
        {
          content: 'Hello, this is a test message!',
          type: 'TEXT',
          timestamp: new Date().toISOString()
        },
        {
          content: 'This is another test message with emojis 😊🎉',
          type: 'TEXT',
          timestamp: new Date().toISOString()
        },
        {
          content: 'A longer test message that spans multiple lines and contains various characters including special ones: !@#$%^&*()_+-=[]{}|;:,.<>?',
          type: 'TEXT',
          timestamp: new Date().toISOString()
        }
      ]
    };
  }

  static async cleanupTestData(): Promise<void> {
    const testDataManager = new TestDataManager();
    await testDataManager.cleanupTestData();
  }

  static async waitForCondition(
    condition: () => Promise<boolean>,
    timeout: number = 5000,
    interval: number = 100
  ): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return;
      }
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    throw new Error(`Condition not met within ${timeout}ms`);
  }

  static async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError!;
  }

  static async measurePerformance<T>(
    operation: () => Promise<T>,
    name: string
  ): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    const result = await operation();
    const duration = Date.now() - startTime;
    
    console.log(`Performance: ${name} took ${duration}ms`);
    
    return { result, duration };
  }

  static async checkConsoleErrors(page: Page): Promise<string[]> {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    return errors;
  }

  static async checkNetworkErrors(page: Page): Promise<string[]> {
    const errors: string[] = [];
    
    page.on('response', response => {
      if (response.status() >= 400) {
        errors.push(`${response.status()} ${response.url()}`);
      }
    });
    
    return errors;
  }

  static async validateAccessibility(page: Page): Promise<void> {
    // Basic accessibility checks
    const headings = await page.locator('h1, h2, h3, h4, h5, h6').count();
    expect(headings).toBeGreaterThan(0);
    
    const images = await page.locator('img').count();
    if (images > 0) {
      const imagesWithAlt = await page.locator('img[alt]').count();
      expect(imagesWithAlt).toBe(images);
    }
    
    const buttons = await page.locator('button').count();
    if (buttons > 0) {
      const buttonsWithLabels = await page.locator('button[aria-label], button:has-text("")').count();
      expect(buttonsWithLabels).toBe(buttons);
    }
  }

  static async simulateUserInteraction(page: Page, selector: string, action: 'click' | 'hover' | 'focus'): Promise<void> {
    const element = page.locator(selector);
    
    switch (action) {
      case 'click':
        await element.click();
        break;
      case 'hover':
        await element.hover();
        break;
      case 'focus':
        await element.focus();
        break;
    }
    
    // Wait for any animations or state changes
    await page.waitForTimeout(100);
  }

  static async fillFormField(page: Page, selector: string, value: string, options?: {
    clear?: boolean;
    delay?: number;
  }): Promise<void> {
    const element = page.locator(selector);
    
    if (options?.clear) {
      await element.clear();
    }
    
    if (options?.delay) {
      await element.type(value, { delay: options.delay });
    } else {
      await element.fill(value);
    }
  }

  static async verifyElementState(page: Page, selector: string, expectedState: {
    visible?: boolean;
    enabled?: boolean;
    text?: string;
    value?: string;
  }): Promise<void> {
    const element = page.locator(selector);
    
    if (expectedState.visible !== undefined) {
      if (expectedState.visible) {
        await expect(element).toBeVisible();
      } else {
        await expect(element).toBeHidden();
      }
    }
    
    if (expectedState.enabled !== undefined) {
      if (expectedState.enabled) {
        await expect(element).toBeEnabled();
      } else {
        await expect(element).toBeDisabled();
      }
    }
    
    if (expectedState.text !== undefined) {
      await expect(element).toContainText(expectedState.text);
    }
    
    if (expectedState.value !== undefined) {
      await expect(element).toHaveValue(expectedState.value);
    }
  }
}
