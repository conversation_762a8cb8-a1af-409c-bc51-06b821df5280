// frontend/src/components/Chat/__tests__/ConversationList.test.tsx
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import ConversationList from '../ConversationList'
import { renderWithStore, mockConversationsData } from '../../../test/utils'

// Mock the useGetConversationsQuery hook
const mockUseGetConversationsQuery = vi.fn()

vi.mock('../../../services/conversationApi', () => ({
  useGetConversationsQuery: () => mockUseGetConversationsQuery()
}))

// Mock the Icon component
vi.mock('../../ui/Icon', () => ({
  Icon: ({ name, size, className }: any) => (
    <div data-testid={`icon-${name}`} className={className} style={{ width: size, height: size }}>
      {name}
    </div>
  )
}))

// Mock date-fns
vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '2 minutes ago')
}))

describe('ConversationList', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockUseGetConversationsQuery.mockReturnValue({
      data: { results: [] },
      isLoading: false,
      error: null
    })
  })

  it('renders loading state', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null
    })

    renderWithStore(<ConversationList currentUserId="user-1" />)

    expect(screen.getByText('Loading conversations...')).toBeInTheDocument()
    expect(screen.getByTestId('icon-loader')).toBeInTheDocument()
  })

  it('renders error state', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: 'Network error'
    })

    renderWithStore(<ConversationList currentUserId="user-1" />)

    expect(screen.getByText('Error loading conversations')).toBeInTheDocument()
    expect(screen.getByText('Network error')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Retry' })).toBeInTheDocument()
  })

  it('renders conversations list', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: mockConversationsData,
      isLoading: false,
      error: null
    })

    renderWithStore(<ConversationList currentUserId="user-1" />)

    // Check that conversations are rendered
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    expect(screen.getByText((content, element) => {
      return element?.textContent === 'jane_smith:'
    })).toBeInTheDocument()
    expect(screen.getByText('Hello there!')).toBeInTheDocument()
    expect(screen.getByText('Test Group')).toBeInTheDocument()
    expect(screen.getByText((content, element) => {
      return element?.textContent === 'bob_wilson:'
    })).toBeInTheDocument()
    expect(screen.getByText('Group message')).toBeInTheDocument()
  })

  it('handles conversation selection', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: mockConversationsData,
      isLoading: false,
      error: null
    })

    const { store } = renderWithStore(<ConversationList currentUserId="user-1" />)

    // Click on a conversation button
    const conversationButton = screen.getByText('Jane Smith').closest('button')
    fireEvent.click(conversationButton!)

    // Check that the conversation was selected in the store
    const state = store.getState()
    expect(state.conversations.selectedConversationId).toBe('conv-1')
  })

  it('shows selected conversation with different styling', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: mockConversationsData,
      isLoading: false,
      error: null
    })

    const initialState = {
      conversations: {
        selectedConversationId: 'conv-1'
      }
    }

    renderWithStore(<ConversationList currentUserId="user-1" />, initialState)

    // Check that the selected conversation has the active class
    const selectedButton = screen.getByText('Jane Smith').closest('button')
    expect(selectedButton).toHaveClass('bg-blue-50', 'border-blue-200')
  })

  it('shows empty state when no conversations', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: { results: [], count: 0, next: null, previous: null },
      isLoading: false,
      error: null
    })

    renderWithStore(<ConversationList currentUserId="user-1" />)

    expect(screen.getByText('No conversations yet')).toBeInTheDocument()
    expect(screen.getByText('Start a new conversation to get started')).toBeInTheDocument()
    expect(screen.getByTestId('icon-message-circle')).toBeInTheDocument()
  })

  it('displays draft conversations', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: { results: [], count: 0, next: null, previous: null },
      isLoading: false,
      error: null
    })

    const initialState = {
      conversations: {
        draftConversations: [
          {
            id: 'draft-1',
            type: 'DIRECT' as const,
            participants: [
              {
                id: 'user-2',
                username: 'new_user',
                first_name: 'New',
                last_name: 'User',
                profile_picture: null
              }
            ],
            isDraft: true as const,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        ]
      }
    }

    renderWithStore(<ConversationList currentUserId="user-1" />, initialState)

    // Check that draft conversation is rendered
    expect(screen.getByText('New User')).toBeInTheDocument()
    expect(screen.getByText('Start a conversation...')).toBeInTheDocument()
  })

  it('displays user avatars correctly', () => {
    const conversationsWithAvatars = {
      results: [
        {
          ...mockConversationsData.results[0],
          participants: [
            {
              id: 'user-1',
              username: 'john_doe',
              first_name: 'John',
              last_name: 'Doe',
              profile_picture: null
            },
            {
              id: 'user-2',
              username: 'jane_smith',
              first_name: 'Jane',
              last_name: 'Smith',
              profile_picture: 'https://example.com/avatar.jpg'
            }
          ]
        }
      ],
      count: 1,
      next: null,
      previous: null
    }

    mockUseGetConversationsQuery.mockReturnValue({
      data: conversationsWithAvatars,
      isLoading: false,
      error: null
    })

    renderWithStore(<ConversationList currentUserId="user-1" />)

    // User with avatar should show image
    const avatar = screen.getByAltText('Jane Smith')
    expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg')
  })

  it('displays user initials when no avatar', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: mockConversationsData,
      isLoading: false,
      error: null
    })

    renderWithStore(<ConversationList currentUserId="user-1" />)

    // Should show initials for users without avatars
    expect(screen.getByText('JS')).toBeInTheDocument() // Jane Smith
  })

  it('displays group conversation icon', () => {
    mockUseGetConversationsQuery.mockReturnValue({
      data: mockConversationsData,
      isLoading: false,
      error: null
    })

    renderWithStore(<ConversationList currentUserId="user-1" />)

    // Group conversation should show users icon
    expect(screen.getByTestId('icon-users')).toBeInTheDocument()
  })
})
