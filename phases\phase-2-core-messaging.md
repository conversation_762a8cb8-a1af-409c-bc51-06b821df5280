# Phase 2: Core Messaging Infrastructure

**Duration**: 3-4 weeks | **Priority**: Critical

## Overview
This phase implements the core messaging functionality including database schema, real-time message delivery, conversation management, and basic messaging UI components. This implementation builds upon the Phase 1 architecture using Prisma for direct database access in the socket server, <PERSON><PERSON> for Node.js validation, Pydantic for Django validation, and custom Tailwind CSS components.

## Prerequisites
- Phase 1 completed successfully
- User authentication system working
- Socket.io server with Prisma and Zod integration
- PostgreSQL database configured with Prisma schema
- Redis for caching
- Custom UI components with Tailwind CSS established

## Database Schema Implementation

### Step 1: Update Prisma Schema for Messaging

The messaging models are already defined in the Phase 1 Prisma schema. We need to ensure the Django models align with the Prisma schema for consistency.

```python
# backend/apps/messaging/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Conversation(models.Model):
    CONVERSATION_TYPES = [
        ('DIRECT', 'Direct Message'),
        ('GROUP', 'Group Chat'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=10, choices=CONVERSATION_TYPES, default='DIRECT')
    name = models.CharField(max_length=100, blank=True, null=True)  # For group chats
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'conversations'
        ordering = ['-updated_at']

    def __str__(self):
        if self.type == 'GROUP':
            return self.name or f"Group {self.id}"
        return f"Direct conversation {self.id}"

class ConversationParticipant(models.Model):
    PARTICIPANT_ROLES = [
        ('ADMIN', 'Admin'),
        ('MEMBER', 'Member'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_memberships')
    role = models.CharField(max_length=10, choices=PARTICIPANT_ROLES, default='MEMBER')
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'conversation_participants'
        unique_together = ['conversation', 'user']

    def __str__(self):
        return f"{self.user.username} in {self.conversation}"

class Message(models.Model):
    MESSAGE_TYPES = [
        ('TEXT', 'Text Message'),
        ('IMAGE', 'Image'),
        ('FILE', 'File'),
        ('SYSTEM', 'System Message'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')
    content = models.TextField()  # Will be encrypted in Phase 3
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='TEXT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'messages'
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.username} in {self.conversation}"
```

### Step 2: Create Pydantic Schemas for Django API

Following the Phase 1 architecture, we use Pydantic for Django API validation:

```python
# backend/apps/messaging/schemas.py
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum
import uuid

class ConversationType(str, Enum):
    DIRECT = "DIRECT"
    GROUP = "GROUP"

class MessageType(str, Enum):
    TEXT = "TEXT"
    IMAGE = "IMAGE"
    FILE = "FILE"
    SYSTEM = "SYSTEM"

class ParticipantRole(str, Enum):
    ADMIN = "ADMIN"
    MEMBER = "MEMBER"

class UserBasic(BaseModel):
    id: uuid.UUID
    username: str
    first_name: str
    last_name: str
    profile_picture: Optional[str] = None

    class Config:
        from_attributes = True

class MessageCreate(BaseModel):
    content: str = Field(..., min_length=1, max_length=4000)
    message_type: MessageType = MessageType.TEXT
    reply_to_id: Optional[uuid.UUID] = None

class MessageResponse(BaseModel):
    id: uuid.UUID
    conversation_id: uuid.UUID
    sender: UserBasic
    content: str
    message_type: MessageType
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ConversationCreate(BaseModel):
    type: ConversationType
    name: Optional[str] = Field(None, max_length=100)
    participant_ids: List[uuid.UUID] = Field(..., min_items=1)

    @validator('name')
    def validate_name_for_group(cls, v, values):
        if values.get('type') == ConversationType.GROUP and not v:
            raise ValueError('Group conversations must have a name')
        return v

class ConversationResponse(BaseModel):
    id: uuid.UUID
    type: ConversationType
    name: Optional[str]
    participants: List[UserBasic]
    last_message: Optional[MessageResponse]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
```

### Step 3: Create and Run Migrations

```bash
# Create messaging app
cd backend
python manage.py startapp messaging

# Add to INSTALLED_APPS in settings.py
# Create migrations
python manage.py makemigrations messaging
python manage.py migrate

# Update Prisma schema to match Django models
cd ../socket-server
npx prisma db pull  # Pull latest schema from database
npx prisma generate  # Regenerate Prisma client
```

## API Implementation

### Step 4: Django API Views with Pydantic Validation

```python
# backend/apps/messaging/views.py
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from django.db.models import Q, Max
from django.utils import timezone
from pydantic import ValidationError
from .models import Conversation, ConversationParticipant, Message
from .schemas import (
    ConversationCreate, ConversationResponse, MessageCreate,
    MessageResponse, UserBasic
)
from users.models import User

class MessagePagination(PageNumberPagination):
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 100

def serialize_user(user):
    """Convert Django User model to Pydantic UserBasic"""
    return UserBasic(
        id=user.id,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        profile_picture=user.profile_picture
    )

def serialize_message(message):
    """Convert Django Message model to Pydantic MessageResponse"""
    return MessageResponse(
        id=message.id,
        conversation_id=message.conversation.id,
        sender=serialize_user(message.sender),
        content=message.content,
        message_type=message.message_type,
        created_at=message.created_at,
        updated_at=message.updated_at
    )

def serialize_conversation(conversation, request_user=None):
    """Convert Django Conversation model to Pydantic ConversationResponse"""
    participants = [
        serialize_user(p.user) for p in conversation.participants.all()
    ]

    last_message = conversation.messages.last()
    last_message_data = serialize_message(last_message) if last_message else None

    return ConversationResponse(
        id=conversation.id,
        type=conversation.type,
        name=conversation.name,
        participants=participants,
        last_message=last_message_data,
        created_at=conversation.created_at,
        updated_at=conversation.updated_at
    )

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def list_conversations(request):
    """List user's conversations"""
    conversations = Conversation.objects.filter(
        participants__user=request.user
    ).distinct().order_by('-updated_at')

    paginator = MessagePagination()
    page = paginator.paginate_queryset(conversations, request)

    conversation_data = [
        serialize_conversation(conv, request.user).dict()
        for conv in page
    ]

    return paginator.get_paginated_response(conversation_data)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_conversation(request):
    """Create a new conversation"""
    try:
        # Validate input with Pydantic
        conversation_data = ConversationCreate(**request.data)

        # For direct messages, check if conversation already exists
        if conversation_data.type == 'DIRECT' and len(conversation_data.participant_ids) == 1:
            other_user_id = conversation_data.participant_ids[0]
            existing_conversation = Conversation.objects.filter(
                type='DIRECT',
                participants__user=request.user
            ).filter(
                participants__user_id=other_user_id
            ).first()

            if existing_conversation:
                return Response(
                    serialize_conversation(existing_conversation, request.user).dict(),
                    status=status.HTTP_200_OK
                )

        # Verify all participant IDs exist
        participant_users = User.objects.filter(id__in=conversation_data.participant_ids)
        if participant_users.count() != len(conversation_data.participant_ids):
            return Response(
                {'error': 'One or more participant IDs are invalid'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create new conversation
        conversation = Conversation.objects.create(
            type=conversation_data.type,
            name=conversation_data.name or ''
        )

        # Add creator as participant
        ConversationParticipant.objects.create(
            conversation=conversation,
            user=request.user,
            role='ADMIN' if conversation_data.type == 'GROUP' else 'MEMBER'
        )

        # Add other participants
        for user_id in conversation_data.participant_ids:
            if user_id != request.user.id:
                ConversationParticipant.objects.create(
                    conversation=conversation,
                    user_id=user_id,
                    role='MEMBER'
                )

        return Response(
            serialize_conversation(conversation, request.user).dict(),
            status=status.HTTP_201_CREATED
        )

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_conversation_messages(request, conversation_id):
    """Get messages for a conversation"""
    conversation = get_object_or_404(Conversation, id=conversation_id)

    # Check if user is participant
    if not conversation.participants.filter(user=request.user).exists():
        return Response(
            {'error': 'Not a participant in this conversation'},
            status=status.HTTP_403_FORBIDDEN
        )

    messages = conversation.messages.order_by('-created_at')

    paginator = MessagePagination()
    page = paginator.paginate_queryset(messages, request)

    message_data = [serialize_message(msg).dict() for msg in page]

    return paginator.get_paginated_response(message_data)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def send_message(request, conversation_id):
    """Send a message to a conversation"""
    try:
        conversation = get_object_or_404(Conversation, id=conversation_id)

        # Check if user is participant
        participant = conversation.participants.filter(user=request.user).first()
        if not participant:
            return Response(
                {'error': 'Not a participant in this conversation'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Validate input with Pydantic
        message_data = MessageCreate(**request.data)

        # Create message
        message = Message.objects.create(
            conversation=conversation,
            sender=request.user,
            content=message_data.content,
            message_type=message_data.message_type
        )

        # Update conversation timestamp
        conversation.updated_at = timezone.now()
        conversation.save(update_fields=['updated_at'])

        # Note: Real-time emission will be handled by the socket server
        # when it detects the new message in the database

        return Response(
            serialize_message(message).dict(),
            status=status.HTTP_201_CREATED
        )

    except ValidationError as e:
        return Response({'errors': e.errors()}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

## Socket.io Integration with Improved Architecture

### Step 5: Service Layer Implementation

Following clean architecture principles, we separate business logic from socket communication. First, let's create the service layer:

```typescript
// socket-server/src/services/messageService.ts
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { MessageCreateSchema, MessageCreate } from '../schemas';

export class MessageService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async createMessage(data: MessageCreate, senderId: string) {
    try {
      // Validate input
      const validatedData = MessageCreateSchema.parse(data);

      // Verify user has access to conversation
      const participant = await this.verifyConversationAccess(
        senderId,
        validatedData.conversationId
      );

      if (!participant) {
        throw new Error('Access denied to conversation');
      }

      // Create message in database
      const message = await this.prisma.message.create({
        data: {
          conversationId: validatedData.conversationId,
          senderId: senderId,
          content: validatedData.content,
          messageType: validatedData.messageType
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true
            }
          }
        }
      });

      // Update conversation timestamp
      await this.prisma.conversation.update({
        where: { id: validatedData.conversationId },
        data: { updatedAt: new Date() }
      });

      return message;
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  async getConversationMessages(conversationId: string, userId: string, page: number = 1, limit: number = 50) {
    try {
      // Verify user has access to conversation
      const hasAccess = await this.verifyConversationAccess(userId, conversationId);
      if (!hasAccess) {
        throw new Error('Access denied to conversation');
      }

      const skip = (page - 1) * limit;

      const messages = await this.prisma.message.findMany({
        where: {
          conversationId: conversationId
        },
        include: {
          sender: {
            select: {
              id: true,
              username: true,
              firstName: true,
              lastName: true,
              profilePicture: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      });

      return messages.reverse(); // Return in chronological order
    } catch (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }
  }

  async verifyConversationAccess(userId: string, conversationId: string) {
    try {
      const participant = await this.prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          userId
        }
      });
      return participant;
    } catch (error) {
      console.error('Error verifying conversation access:', error);
      return null;
    }
  }

  async updateUserStatus(userId: string, isOnline: boolean) {
    try {
      await this.prisma.user.update({
        where: { id: userId },
        data: { lastSeen: new Date() }
      });
      return true;
    } catch (error) {
      console.error('Error updating user status:', error);
      return false;
    }
  }
}
```

```typescript
// socket-server/src/services/conversationService.ts
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { ConversationCreateSchema, ConversationCreate } from '../schemas';

export class ConversationService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async getUserConversations(userId: string) {
    try {
      const conversations = await this.prisma.conversation.findMany({
        where: {
          participants: {
            some: {
              userId: userId
            }
          }
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                  profilePicture: true
                }
              }
            }
          },
          messages: {
            take: 1,
            orderBy: {
              createdAt: 'desc'
            },
            include: {
              sender: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true
                }
              }
            }
          }
        },
        orderBy: {
          updatedAt: 'desc'
        }
      });

      return conversations.map(conv => ({
        ...conv,
        lastMessage: conv.messages[0] || null
      }));
    } catch (error) {
      console.error('Error fetching user conversations:', error);
      throw error;
    }
  }

  async createConversation(data: ConversationCreate, creatorId: string) {
    try {
      // Validate input
      const validatedData = ConversationCreateSchema.parse(data);

      // For direct messages, check if conversation already exists
      if (validatedData.type === 'DIRECT' && validatedData.participantIds.length === 1) {
        const otherUserId = validatedData.participantIds[0];
        const existingConversation = await this.findDirectConversation(creatorId, otherUserId);

        if (existingConversation) {
          return existingConversation;
        }
      }

      // Create new conversation
      const conversation = await this.prisma.conversation.create({
        data: {
          type: validatedData.type,
          name: validatedData.name || null
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  firstName: true,
                  lastName: true,
                  profilePicture: true
                }
              }
            }
          }
        }
      });

      // Add creator as participant
      await this.prisma.conversationParticipant.create({
        data: {
          conversationId: conversation.id,
          userId: creatorId,
          role: validatedData.type === 'GROUP' ? 'ADMIN' : 'MEMBER'
        }
      });

      // Add other participants
      for (const participantId of validatedData.participantIds) {
        if (participantId !== creatorId) {
          await this.prisma.conversationParticipant.create({
            data: {
              conversationId: conversation.id,
              userId: participantId,
              role: 'MEMBER'
            }
          });
        }
      }

      return conversation;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw error;
    }
  }

  private async findDirectConversation(userId1: string, userId2: string) {
    const conversation = await this.prisma.conversation.findFirst({
      where: {
        type: 'DIRECT',
        participants: {
          every: {
            userId: {
              in: [userId1, userId2]
            }
          }
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
                profilePicture: true
              }
            }
          }
        }
      }
    });

    return conversation;
  }

  async joinConversation(userId: string, conversationId: string) {
    try {
      const participant = await this.prisma.conversationParticipant.findFirst({
        where: {
          conversationId,
          userId
        }
      });

      return !!participant;
    } catch (error) {
      console.error('Error joining conversation:', error);
      return false;
    }
  }
}
```

### Step 6: Socket Event Handler Separation

Now let's create thin socket event handlers that delegate to the service layer:

```typescript
// socket-server/src/events/socketEvents.ts
import { Server, Socket } from 'socket.io';
import { MessageService } from '../services/messageService';
import { ConversationService } from '../services/conversationService';
import { z } from 'zod';
import {
  MessageCreateSchema,
  JoinRoomSchema,
  TypingEventSchema
} from '../schemas';

interface AuthenticatedSocket extends Socket {
  userId: string;
  user: any;
}

export class SocketEventHandler {
  private io: Server;
  private messageService: MessageService;
  private conversationService: ConversationService;

  constructor(
    io: Server,
    messageService: MessageService,
    conversationService: ConversationService
  ) {
    this.io = io;
    this.messageService = messageService;
    this.conversationService = conversationService;
  }

  handleConnection(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} connected to messaging`);

    // Join user to their conversations
    socket.on('join_conversations', () => this.handleJoinConversations(socket));

    // Handle new message creation
    socket.on('send_message', (data) => this.handleSendMessage(socket, data));

    // Handle typing indicators
    socket.on('typing_start', (data) => this.handleTypingStart(socket, data));
    socket.on('typing_stop', (data) => this.handleTypingStop(socket, data));

    // Handle joining specific conversation room
    socket.on('join_conversation', (data) => this.handleJoinConversation(socket, data));

    // Handle user status
    socket.on('user_online', () => this.handleUserOnline(socket));

    // Handle disconnection
    socket.on('disconnect', () => this.handleDisconnect(socket));
  }

  private async handleJoinConversations(socket: AuthenticatedSocket) {
    try {
      const conversations = await this.conversationService.getUserConversations(socket.userId);

      // Join all conversation rooms
      for (const conversation of conversations) {
        socket.join(`conversation_${conversation.id}`);
      }

      socket.emit('conversations_joined', {
        success: true,
        count: conversations.length,
        conversations: conversations
      });
    } catch (error) {
      console.error('Error joining conversations:', error);
      socket.emit('error', { message: 'Failed to join conversations' });
    }
  }

  private async handleSendMessage(socket: AuthenticatedSocket, data: any) {
    try {
      const message = await this.messageService.createMessage(data, socket.userId);

      // Emit to all participants in the conversation
      this.io.to(`conversation_${message.conversationId}`).emit('new_message', {
        id: message.id,
        conversationId: message.conversationId,
        sender: message.sender,
        content: message.content,
        messageType: message.messageType,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt
      });

      // Acknowledge to sender
      socket.emit('message_sent', {
        tempId: data.tempId, // For optimistic UI updates
        messageId: message.id,
        status: 'delivered'
      });

    } catch (error) {
      console.error('Error sending message:', error);
      if (error instanceof z.ZodError) {
        socket.emit('error', {
          message: 'Invalid message data',
          details: error.errors
        });
      } else {
        socket.emit('error', { message: error.message || 'Failed to send message' });
      }
    }
  }

  private async handleTypingStart(socket: AuthenticatedSocket, data: any) {
    try {
      const typingData = TypingEventSchema.parse(data);

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: true
        });
      }
    } catch (error) {
      console.error('Error handling typing start:', error);
    }
  }

  private async handleTypingStop(socket: AuthenticatedSocket, data: any) {
    try {
      const typingData = TypingEventSchema.parse(data);

      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: false
        });
      }
    } catch (error) {
      console.error('Error handling typing stop:', error);
    }
  }

  private async handleJoinConversation(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId } = JoinRoomSchema.parse(data);

      const hasAccess = await this.conversationService.joinConversation(socket.userId, conversationId);
      if (hasAccess) {
        socket.join(`conversation_${conversationId}`);
        socket.emit('joined_conversation', { conversationId });
      } else {
        socket.emit('error', { message: 'Access denied to conversation' });
      }
    } catch (error) {
      console.error('Error joining conversation:', error);
      socket.emit('error', { message: 'Failed to join conversation' });
    }
  }

  private async handleUserOnline(socket: AuthenticatedSocket) {
    try {
      await this.messageService.updateUserStatus(socket.userId, true);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'online'
      });
    } catch (error) {
      console.error('Error updating user online status:', error);
    }
  }

  private async handleDisconnect(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} disconnected from messaging`);

    try {
      await this.messageService.updateUserStatus(socket.userId, false);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'offline'
      });
    } catch (error) {
      console.error('Error updating user offline status:', error);
    }
  }
}
```

## Frontend Implementation with Redux Toolkit Architecture

### Step 7: Redux Toolkit Slice Implementation

Following clean architecture principles, we implement Redux slices with proper separation of concerns:

```typescript
// frontend/src/store/slices/messageSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface Message {
  id: string;
  conversationId: string;
  sender: {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  content: string;
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
  createdAt: string;
  updatedAt: string;
}

export interface MessageState {
  messages: Record<string, Message[]>; // conversationId -> messages
  loading: boolean;
  error: string | null;
  sendingMessages: Record<string, boolean>; // tempId -> sending status
  typingUsers: Record<string, string[]>; // conversationId -> userIds
}

const initialState: MessageState = {
  messages: {},
  loading: false,
  error: null,
  sendingMessages: {},
  typingUsers: {}
};

// Async thunks
export const fetchMessages = createAsyncThunk(
  'messages/fetchMessages',
  async ({ conversationId, page = 1 }: { conversationId: string; page?: number }) => {
    const response = await axios.get(`/api/messaging/conversations/${conversationId}/messages/`, {
      params: { page }
    });
    return { conversationId, messages: response.data.results, page };
  }
);

export const sendMessage = createAsyncThunk(
  'messages/sendMessage',
  async ({
    conversationId,
    content,
    messageType = 'TEXT',
    tempId
  }: {
    conversationId: string;
    content: string;
    messageType?: string;
    tempId: string;
  }) => {
    const response = await axios.post(`/api/messaging/conversations/${conversationId}/send/`, {
      content,
      messageType
    });
    return { ...response.data, tempId };
  }
);

const messageSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {
    // Socket event handlers
    addMessage: (state, action: PayloadAction<Message>) => {
      const message = action.payload;
      if (!state.messages[message.conversationId]) {
        state.messages[message.conversationId] = [];
      }
      state.messages[message.conversationId].push(message);
    },

    addOptimisticMessage: (state, action: PayloadAction<{
      tempId: string;
      conversationId: string;
      content: string;
      sender: Message['sender'];
      messageType: string;
    }>) => {
      const { tempId, conversationId, content, sender, messageType } = action.payload;

      if (!state.messages[conversationId]) {
        state.messages[conversationId] = [];
      }

      const optimisticMessage: Message = {
        id: tempId,
        conversationId,
        sender,
        content,
        messageType: messageType as Message['messageType'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      state.messages[conversationId].push(optimisticMessage);
      state.sendingMessages[tempId] = true;
    },

    updateOptimisticMessage: (state, action: PayloadAction<{
      tempId: string;
      message: Message;
    }>) => {
      const { tempId, message } = action.payload;

      // Find and replace the optimistic message
      const conversationMessages = state.messages[message.conversationId];
      if (conversationMessages) {
        const index = conversationMessages.findIndex(msg => msg.id === tempId);
        if (index !== -1) {
          conversationMessages[index] = message;
        }
      }

      delete state.sendingMessages[tempId];
    },

    setTypingUsers: (state, action: PayloadAction<{
      conversationId: string;
      userId: string;
      isTyping: boolean;
    }>) => {
      const { conversationId, userId, isTyping } = action.payload;

      if (!state.typingUsers[conversationId]) {
        state.typingUsers[conversationId] = [];
      }

      const typingUsers = state.typingUsers[conversationId];
      const userIndex = typingUsers.indexOf(userId);

      if (isTyping && userIndex === -1) {
        typingUsers.push(userId);
      } else if (!isTyping && userIndex !== -1) {
        typingUsers.splice(userIndex, 1);
      }
    },

    clearMessages: (state, action: PayloadAction<string>) => {
      const conversationId = action.payload;
      delete state.messages[conversationId];
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMessages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.loading = false;
        const { conversationId, messages, page } = action.payload;

        if (page === 1) {
          state.messages[conversationId] = messages;
        } else {
          // Prepend older messages for pagination
          state.messages[conversationId] = [
            ...messages,
            ...(state.messages[conversationId] || [])
          ];
        }
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch messages';
      })
      .addCase(sendMessage.pending, (state, action) => {
        // Optimistic message is already added, just track sending state
        const tempId = action.meta.arg.tempId;
        state.sendingMessages[tempId] = true;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        const { tempId, ...message } = action.payload;

        // Replace optimistic message with real message
        const conversationMessages = state.messages[message.conversationId];
        if (conversationMessages) {
          const index = conversationMessages.findIndex(msg => msg.id === tempId);
          if (index !== -1) {
            conversationMessages[index] = message;
          }
        }

        delete state.sendingMessages[tempId];
      })
      .addCase(sendMessage.rejected, (state, action) => {
        const tempId = action.meta.arg.tempId;
        const conversationId = action.meta.arg.conversationId;

        // Remove failed optimistic message
        const conversationMessages = state.messages[conversationId];
        if (conversationMessages) {
          const index = conversationMessages.findIndex(msg => msg.id === tempId);
          if (index !== -1) {
            conversationMessages.splice(index, 1);
          }
        }

        delete state.sendingMessages[tempId];
        state.error = action.error.message || 'Failed to send message';
      });
  }
});

export const {
  addMessage,
  addOptimisticMessage,
  updateOptimisticMessage,
  setTypingUsers,
  clearMessages,
  setError
} = messageSlice.actions;

export default messageSlice.reducer;
```

```typescript
// frontend/src/store/slices/conversationSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

export interface Conversation {
  id: string;
  type: 'DIRECT' | 'GROUP';
  name?: string;
  participants: Array<{
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  }>;
  lastMessage?: {
    id: string;
    content: string;
    sender: {
      username: string;
    };
    createdAt: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ConversationState {
  conversations: Conversation[];
  selectedConversationId: string | null;
  loading: boolean;
  error: string | null;
  creating: boolean;
}

const initialState: ConversationState = {
  conversations: [],
  selectedConversationId: null,
  loading: false,
  error: null,
  creating: false
};

// Async thunks
export const fetchConversations = createAsyncThunk(
  'conversations/fetchConversations',
  async () => {
    const response = await axios.get('/api/messaging/conversations/');
    return response.data.results || response.data;
  }
);

export const createConversation = createAsyncThunk(
  'conversations/createConversation',
  async ({
    type,
    name,
    participantIds
  }: {
    type: 'DIRECT' | 'GROUP';
    name?: string;
    participantIds: string[]
  }) => {
    const response = await axios.post('/api/messaging/conversations/create/', {
      type,
      name,
      participant_ids: participantIds
    });
    return response.data;
  }
);

const conversationSlice = createSlice({
  name: 'conversations',
  initialState,
  reducers: {
    selectConversation: (state, action: PayloadAction<string>) => {
      state.selectedConversationId = action.payload;
    },

    updateConversationLastMessage: (state, action: PayloadAction<{
      conversationId: string;
      message: {
        id: string;
        content: string;
        sender: { username: string };
        createdAt: string;
      };
    }>) => {
      const { conversationId, message } = action.payload;
      const conversation = state.conversations.find(c => c.id === conversationId);
      if (conversation) {
        conversation.lastMessage = message;
        conversation.updatedAt = message.createdAt;

        // Move conversation to top
        const index = state.conversations.indexOf(conversation);
        state.conversations.splice(index, 1);
        state.conversations.unshift(conversation);
      }
    },

    addConversation: (state, action: PayloadAction<Conversation>) => {
      const newConversation = action.payload;
      const existingIndex = state.conversations.findIndex(c => c.id === newConversation.id);

      if (existingIndex === -1) {
        state.conversations.unshift(newConversation);
      } else {
        state.conversations[existingIndex] = newConversation;
      }
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchConversations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations = action.payload;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch conversations';
      })
      .addCase(createConversation.pending, (state) => {
        state.creating = true;
        state.error = null;
      })
      .addCase(createConversation.fulfilled, (state, action) => {
        state.creating = false;
        const newConversation = action.payload;

        // Add to conversations if not already present
        const existingIndex = state.conversations.findIndex(c => c.id === newConversation.id);
        if (existingIndex === -1) {
          state.conversations.unshift(newConversation);
        }

        // Select the new conversation
        state.selectedConversationId = newConversation.id;
      })
      .addCase(createConversation.rejected, (state, action) => {
        state.creating = false;
        state.error = action.error.message || 'Failed to create conversation';
      });
  }
});

export const {
  selectConversation,
  updateConversationLastMessage,
  addConversation,
  setError
} = conversationSlice.actions;

export default conversationSlice.reducer;
```

### Step 8: Message Components with Redux Integration

Following the Phase 1 architecture, we use custom Tailwind CSS components with Redux integration:

```typescript
// frontend/src/components/Chat/MessageList.tsx
import React, { useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { formatDistanceToNow } from 'date-fns';
import { Icon } from '../ui/Icon';
import { RootState } from '../../store';
import { Message } from '../../store/slices/messageSlice';

interface MessageListProps {
  conversationId: string;
  currentUserId: string;
  onReply?: (message: Message) => void;
}

const MessageList: React.FC<MessageListProps> = ({
  conversationId,
  currentUserId,
  onReply
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const messages = useSelector((state: RootState) =>
    state.messages.messages[conversationId] || []
  );
  const sendingMessages = useSelector((state: RootState) =>
    state.messages.sendingMessages
  );
  const typingUsers = useSelector((state: RootState) =>
    state.messages.typingUsers[conversationId] || []
  );

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName[0] || ''}${lastName[0] || ''}`.toUpperCase();
  };

  const isMessageSending = (messageId: string) => {
    return sendingMessages[messageId] || false;
  };

  return (
    <div className="flex-1 overflow-auto p-4 space-y-4">
      {messages.map((message) => {
        const isOwnMessage = message.sender.id === currentUserId;
        const isSending = isMessageSending(message.id);

        return (
          <div
            key={message.id}
            className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
          >
            <div className={`flex max-w-[70%] items-end space-x-2 ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {!isOwnMessage && (
                <div className="flex-shrink-0">
                  {message.sender.profilePicture ? (
                    <img
                      src={message.sender.profilePicture}
                      alt={`${message.sender.firstName} ${message.sender.lastName}`}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-gray-400 flex items-center justify-center text-white text-sm font-medium">
                      {getInitials(message.sender.firstName, message.sender.lastName)}
                    </div>
                  )}
                </div>
              )}

              <div
                className={`relative group rounded-lg px-4 py-2 max-w-full ${
                  isOwnMessage
                    ? 'bg-blue-600 text-white rounded-br-none'
                    : 'bg-gray-100 text-gray-900 rounded-bl-none'
                } ${isSending ? 'opacity-70' : ''}`}
              >
                <div className="text-sm leading-relaxed">
                  {message.content}
                </div>

                <div className={`flex items-center justify-between mt-1 ${
                  isOwnMessage ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  <div className="text-xs opacity-70">
                    {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                  </div>

                  {isSending && (
                    <div className="flex items-center space-x-1 text-xs">
                      <Icon name="spinner" size={12} className="animate-spin" />
                      <span>Sending...</span>
                    </div>
                  )}
                </div>

                {/* Message actions */}
                {onReply && !isSending && (
                  <button
                    onClick={() => onReply(message)}
                    className={`absolute top-0 right-0 transform translate-x-8 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full ${
                      isOwnMessage ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-600'
                    } hover:bg-opacity-80`}
                    title="Reply to message"
                  >
                    <Icon name="message" size={14} />
                  </button>
                )}
              </div>
            </div>
          </div>
        );
      })}

      {/* Typing indicators */}
      {typingUsers.length > 0 && (
        <div className="flex justify-start">
          <div className="bg-gray-100 rounded-lg px-4 py-2 text-gray-600 text-sm">
            <div className="flex items-center space-x-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span>
                {typingUsers.length === 1 ? 'Someone is' : `${typingUsers.length} people are`} typing...
              </span>
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
```

### Step 9: Message Input Component with Redux Integration

```typescript
// frontend/src/components/Chat/MessageInput.tsx
import React, { useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Button } from '../ui/Button';
import { Icon } from '../ui/Icon';
import { useSocket } from '../../contexts/SocketContext';
import { RootState } from '../../store';

interface MessageInputProps {
  conversationId: string;
  disabled?: boolean;
  replyTo?: {
    id: string;
    content: string;
    sender: string;
  } | null;
  onCancelReply?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  conversationId,
  disabled = false,
  replyTo,
  onCancelReply,
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const { sendMessage, startTyping, stopTyping, isConnected } = useSocket();
  const loading = useSelector((state: RootState) => state.messages.loading);

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = event.target.value;
    setMessage(value);

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }

    // Handle typing indicators
    if (value && !isTyping && isConnected) {
      setIsTyping(true);
      startTyping(conversationId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping && isConnected) {
        setIsTyping(false);
        stopTyping(conversationId);
      }
    }, 1000);
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    if (message.trim() && !disabled && isConnected) {
      // Send message through SocketContext with optimistic update
      sendMessage(conversationId, message.trim());

      setMessage('');

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }

      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false);
        stopTyping(conversationId);
      }

      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit(event);
    }
  };

  return (
    <div className="border-t border-gray-200 bg-white p-4">
      {/* Connection status indicator */}
      {!isConnected && (
        <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center space-x-2 text-yellow-800 text-sm">
            <Icon name="alert" size={16} />
            <span>Reconnecting to chat server...</span>
          </div>
        </div>
      )}

      {/* Reply indicator */}
      {replyTo && (
        <div className="mb-3 p-3 bg-gray-50 rounded-lg border-l-4 border-blue-500">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-gray-900">
                Replying to {replyTo.sender}
              </div>
              <div className="text-sm text-gray-600 truncate">
                {replyTo.content}
              </div>
            </div>
            {onCancelReply && (
              <button
                onClick={onCancelReply}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <Icon name="close" size={16} />
              </button>
            )}
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        {/* Attachment button */}
        <button
          type="button"
          disabled={disabled || !isConnected}
          className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <Icon name="plus" size={20} />
        </button>

        {/* Message input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={isConnected ? "Type a message..." : "Connecting..."}
            disabled={disabled || loading || !isConnected}
            rows={1}
            className="w-full resize-none rounded-lg border border-gray-300 px-4 py-2 text-sm placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ minHeight: '40px', maxHeight: '120px' }}
          />
        </div>

        {/* Send button */}
        <Button
          type="submit"
          disabled={!message.trim() || disabled || loading || !isConnected}
          icon="send"
          className="flex-shrink-0"
          loading={loading}
        >
          Send
        </Button>
      </form>
    </div>
  );
};

export default MessageInput;
```

### Step 10: Conversation List Component with Redux Integration

```typescript
// frontend/src/components/Chat/ConversationList.tsx
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { formatDistanceToNow } from 'date-fns';
import { Icon } from '../ui/Icon';
import { RootState, AppDispatch } from '../../store';
import {
  fetchConversations,
  selectConversation,
  Conversation
} from '../../store/slices/conversationSlice';

interface ConversationListProps {
  currentUserId: string;
}

const ConversationList: React.FC<ConversationListProps> = ({
  currentUserId
}) => {
  const dispatch = useDispatch<AppDispatch>();

  const {
    conversations,
    selectedConversationId,
    loading,
    error
  } = useSelector((state: RootState) => state.conversations);

  useEffect(() => {
    dispatch(fetchConversations());
  }, [dispatch]);

  const handleSelectConversation = (conversationId: string) => {
    dispatch(selectConversation(conversationId));
  };

  const getConversationName = (conversation: Conversation) => {
    if (conversation.type === 'GROUP') {
      return conversation.name || 'Group Chat';
    }

    // For direct messages, show the other participant's name
    const otherParticipant = conversation.participants.find(
      p => p.id !== currentUserId
    );
    return otherParticipant
      ? `${otherParticipant.firstName} ${otherParticipant.lastName}`
      : 'Unknown User';
  };

  const getConversationAvatar = (conversation: Conversation) => {
    if (conversation.type === 'GROUP') {
      return (
        <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
          <Icon name="user" size={20} />
        </div>
      );
    }

    const otherParticipant = conversation.participants.find(
      p => p.id !== currentUserId
    );

    if (otherParticipant?.profilePicture) {
      return (
        <img
          src={otherParticipant.profilePicture}
          alt={`${otherParticipant.firstName} ${otherParticipant.lastName}`}
          className="w-12 h-12 rounded-full object-cover"
        />
      );
    }

    const initials = otherParticipant
      ? `${otherParticipant.firstName[0]}${otherParticipant.lastName[0]}`.toUpperCase()
      : '?';

    return (
      <div className="w-12 h-12 rounded-full bg-gray-400 flex items-center justify-center text-white font-medium">
        {initials}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex items-center space-x-2 text-gray-500">
          <Icon name="spinner" size={20} className="animate-spin" />
          <span>Loading conversations...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <div className="text-center text-red-500">
          <Icon name="alert" size={48} className="mx-auto mb-2" />
          <p className="font-medium">Error loading conversations</p>
          <p className="text-sm">{error}</p>
          <button
            onClick={() => dispatch(fetchConversations())}
            className="mt-2 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      {conversations.map((conversation) => {
        const isSelected = conversation.id === selectedConversationId;

        return (
          <button
            key={conversation.id}
            onClick={() => handleSelectConversation(conversation.id)}
            className={`w-full p-4 text-left hover:bg-gray-50 transition-colors border-b border-gray-100 ${
              isSelected ? 'bg-blue-50 border-blue-200' : ''
            }`}
          >
            <div className="flex items-center space-x-3">
              {getConversationAvatar(conversation)}

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-gray-900 truncate">
                    {getConversationName(conversation)}
                  </h3>
                  <span className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(conversation.updatedAt), { addSuffix: true })}
                  </span>
                </div>

                {conversation.lastMessage && (
                  <p className="text-sm text-gray-600 truncate mt-1">
                    <span className="font-medium">
                      {conversation.lastMessage.sender.username}:
                    </span>{' '}
                    {conversation.lastMessage.content}
                  </p>
                )}
              </div>
            </div>
          </button>
        );
      })}

      {conversations.length === 0 && !loading && (
        <div className="p-8 text-center text-gray-500">
          <Icon name="message" size={48} className="mx-auto mb-4 text-gray-300" />
          <p>No conversations yet</p>
          <p className="text-sm">Start a new conversation to get started</p>
        </div>
      )}
    </div>
  );
};

export default ConversationList;
```

## Integration Points with Centralized SocketContext Architecture

### Django ↔ Socket Server (Service Layer Architecture)
- **Direct Database Access**: Socket server uses Prisma through service layer for database operations
- **Service Layer Separation**: Business logic separated from socket communication in dedicated service classes
- **Shared Schema**: Both Django and Prisma use the same database schema with consistent field names
- **Validation Consistency**: Pydantic (Django) and Zod (Socket server) schemas ensure data consistency
- **Clean Architecture**: Socket events act as thin wrappers that delegate to service methods

### Frontend ↔ Backend (SocketContext + Redux Architecture)
- **REST API**: Django provides REST endpoints for conversation management and message history
- **Centralized Socket Management**: SocketContext serves as single source of truth for all socket operations
- **Redux State Management**: Centralized state management with Redux Toolkit slices
- **Real-time Communication**: Socket.io handled exclusively through SocketContext
- **Optimistic Updates**: Frontend provides immediate feedback with optimistic message updates
- **Custom UI Components**: Tailwind CSS components integrated with SocketContext and Redux state
- **Type Safety**: TypeScript interfaces ensure consistency across all layers

### SocketContext Architecture Benefits
- **Single Source of Truth**: All socket operations centralized in one context
- **Clean Component Interface**: Components only interact with socket through context methods
- **Automatic Reconnection**: Built-in connection management and status tracking
- **Redux Integration**: Seamless integration with Redux state management
- **Event Consolidation**: All socket event handling in one place
- **Memory Management**: Proper cleanup and event listener management

### Socket Server Architecture Benefits
- **Service Layer**: Business logic separated from socket communication
- **Single Responsibility**: Each service class handles one domain (messages, conversations)
- **Testability**: Services can be unit tested independently of socket infrastructure
- **Maintainability**: Clear separation makes code easier to understand and modify

### Frontend Architecture Benefits
- **Centralized Socket Management**: No scattered socket connections throughout the app
- **Redux Toolkit**: Modern Redux patterns with reduced boilerplate
- **Async Thunks**: Proper handling of asynchronous operations
- **Optimistic Updates**: Better user experience with immediate feedback
- **Connection Status**: Real-time connection status available to all components
- **Type Safety**: Full TypeScript integration throughout the entire flow
- **Provider Pattern**: Clean dependency injection through React context

## Acceptance Criteria

### Phase 2 Completion Checklist

#### Backend Architecture
- [ ] Database models created and migrated (aligned with Prisma schema)
- [ ] Pydantic schemas implemented for Django API validation
- [ ] Django API endpoints functional with proper error handling
- [ ] URL configuration properly set up

#### Socket Server Architecture
- [ ] Service layer implemented (MessageService, ConversationService)
- [ ] Socket event handlers separated from business logic
- [ ] Zod schemas working for real-time validation
- [ ] Prisma integration for direct database access
- [ ] Real-time message delivery via Socket.io
- [ ] Typing indicators working through socket server
- [ ] User status tracking (online/offline)

#### Frontend Architecture
- [ ] SocketContext implemented as single source of truth for socket operations
- [ ] SocketProvider properly wrapping the application
- [ ] Redux Toolkit slices implemented (messageSlice, conversationSlice)
- [ ] Async thunks for API operations
- [ ] Custom Tailwind CSS components (no Material-UI)
- [ ] Optimistic message updates working
- [ ] Real-time updates integrated with Redux state through SocketContext
- [ ] Connection status tracking and display
- [ ] Proper socket cleanup and reconnection handling

#### UI Components
- [ ] ConversationList component with Redux integration
- [ ] MessageList component with typing indicators
- [ ] MessageInput component with socket integration
- [ ] Loading states and error handling
- [ ] Responsive design with Tailwind CSS

#### Integration Features
- [ ] Message history pagination implemented
- [ ] Conversation creation working
- [ ] Real-time message delivery
- [ ] Typing indicators functional
- [ ] User authentication with socket server
- [ ] Optimistic UI updates

### Testing Requirements

#### Backend Testing
- [ ] Unit tests for Django models and Pydantic schemas
- [ ] API endpoint integration tests with Pydantic validation
- [ ] Database consistency tests between Django and Prisma

#### Socket Server Testing
- [ ] Unit tests for service layer (MessageService, ConversationService)
- [ ] Socket event handling tests with Zod validation
- [ ] Prisma database operation tests
- [ ] Socket authentication tests
- [ ] Real-time messaging flow tests

#### Frontend Testing
- [ ] SocketContext tests (connection management, event handling, Redux integration)
- [ ] Redux slice tests (actions, reducers, thunks)
- [ ] Component tests for custom UI components with SocketContext
- [ ] Integration tests for SocketContext + Redux flow
- [ ] Optimistic update tests
- [ ] Connection status and reconnection tests
- [ ] Socket cleanup and memory leak tests

#### End-to-End Testing
- [ ] Complete messaging flow tests
- [ ] Multi-user conversation tests
- [ ] Real-time synchronization tests
- [ ] Error handling and recovery tests

## Common Issues & Troubleshooting

### Database Schema Synchronization
- Ensure Django models match Prisma schema exactly
- Use consistent field names and types between Django and Prisma
- Run `npx prisma db pull` after Django migrations to sync Prisma schema
- Verify enum values match between Django choices and Prisma enums

### Schema Validation Issues
- Ensure Pydantic and Zod schemas are synchronized
- Check for proper error handling in validation
- Verify consistent field naming across services (camelCase vs snake_case)
- Test validation with invalid data to ensure proper error responses

### Socket Server Database Access
- Verify Prisma client is properly initialized and connected
- Check database connection string in socket server environment
- Ensure proper error handling for database operations
- Monitor database connection pooling and performance

### Message Ordering Issues
- Ensure proper timestamp handling across services
- Use database-generated timestamps for consistency
- Handle client-server time differences
- Implement proper message ordering in frontend

### Socket Connection Problems
- Verify JWT token in socket handshake matches Django token format
- Check room joining/leaving logic with database verification
- Monitor connection state and implement reconnection logic
- Test socket authentication with expired tokens

### Frontend Component Issues
- Ensure custom Tailwind components are properly styled
- Test responsive design across different screen sizes
- Verify icon components are working correctly
- Check for proper TypeScript type definitions

### Performance Issues
- Implement message pagination with proper database queries
- Use database indexes on frequently queried fields (conversation_id, created_at)
- Consider message caching strategies with Redis
- Monitor Prisma query performance and optimize as needed

## Next Phase Dependencies
- Core messaging must be fully functional with Prisma integration
- Real-time delivery working reliably through socket server
- Message persistence confirmed in PostgreSQL
- Custom UI components responsive and tested
- Schema validation working consistently across all services
- Database access patterns established and optimized

### Step 9: URL Configuration

```python
# backend/apps/messaging/urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('conversations/', views.list_conversations, name='conversation-list'),
    path('conversations/create/', views.create_conversation, name='create-conversation'),
    path('conversations/<uuid:conversation_id>/messages/', views.get_conversation_messages, name='conversation-messages'),
    path('conversations/<uuid:conversation_id>/send/', views.send_message, name='send-message'),
]

# backend/chatapp/urls.py - Add to main urlpatterns
path('api/messaging/', include('messaging.urls')),
```

### Step 10: Socket Server Integration

```typescript
// socket-server/src/server.ts - Updated with message handlers
import { MessageHandler } from './handlers/messageHandlers';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();
const messageHandler = new MessageHandler(io, prisma, redisClient);

io.on('connection', (socket) => {
  console.log(`User ${socket.userId} connected`);

  // Initialize message handlers
  messageHandler.handleConnection(socket);

  // Existing connection logic from Phase 1...
});

// Graceful shutdown
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  await redisClient.quit();
  process.exit(0);
});
```

### Step 11: Centralized SocketContext Implementation

Following clean architecture principles, we implement a centralized SocketContext that serves as the single source of truth for all socket operations:

```typescript
// frontend/src/contexts/SocketContext.tsx
import React, { createContext, useContext, useEffect, useRef, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { io, Socket } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';
import { useAuth } from './AuthContext';
import { AppDispatch } from '../store';
import {
  addMessage,
  addOptimisticMessage,
  updateOptimisticMessage,
  setTypingUsers,
  setError as setMessageError
} from '../store/slices/messageSlice';
import {
  updateConversationLastMessage,
  addConversation,
  setError as setConversationError
} from '../store/slices/conversationSlice';

interface SocketContextType {
  // Connection status
  isConnected: boolean;

  // Message operations
  sendMessage: (conversationId: string, content: string, messageType?: string) => string;

  // Conversation operations
  joinConversations: () => void;
  joinConversation: (conversationId: string) => void;

  // Typing indicators
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;

  // User status
  setUserOnline: () => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = React.useState(false);
  const { token, isAuthenticated, user } = useAuth();
  const dispatch = useDispatch<AppDispatch>();

  // Initialize socket connection
  useEffect(() => {
    if (isAuthenticated && token) {
      console.log('Initializing socket connection...');

      socketRef.current = io(process.env.REACT_APP_SOCKET_URL || 'http://localhost:3001', {
        auth: {
          token: token
        }
      });

      setupEventListeners();

      return () => {
        cleanup();
      };
    }
  }, [isAuthenticated, token, dispatch]);

  // Setup all socket event listeners
  const setupEventListeners = useCallback(() => {
    if (!socketRef.current) return;

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('error', handleError);

    // Conversation events
    socket.on('conversations_joined', handleConversationsJoined);
    socket.on('joined_conversation', handleJoinedConversation);

    // Message events
    socket.on('new_message', handleNewMessage);
    socket.on('message_sent', handleMessageSent);

    // Typing events
    socket.on('user_typing', handleUserTyping);

    // User status events
    socket.on('user_status_change', handleUserStatusChange);
  }, [dispatch]);

  // Event handlers
  const handleConnect = useCallback(() => {
    console.log('Connected to socket server');
    setIsConnected(true);

    // Auto-join conversations on connect
    if (socketRef.current) {
      socketRef.current.emit('join_conversations');
      socketRef.current.emit('user_online');
    }
  }, []);

  const handleDisconnect = useCallback(() => {
    console.log('Disconnected from socket server');
    setIsConnected(false);
  }, []);

  const handleError = useCallback((error: { message: string; details?: any }) => {
    console.error('Socket error:', error);
    dispatch(setMessageError(error.message));
  }, [dispatch]);

  const handleConversationsJoined = useCallback((data: {
    success: boolean;
    count: number;
    conversations?: any[]
  }) => {
    console.log(`Joined ${data.count} conversations`);

    if (data.conversations) {
      data.conversations.forEach(conversation => {
        dispatch(addConversation(conversation));
      });
    }
  }, [dispatch]);

  const handleJoinedConversation = useCallback((data: { conversationId: string }) => {
    console.log(`Joined conversation: ${data.conversationId}`);
  }, []);

  const handleNewMessage = useCallback((message: any) => {
    // Add message to Redux store
    dispatch(addMessage(message));

    // Update conversation's last message
    dispatch(updateConversationLastMessage({
      conversationId: message.conversationId,
      message: {
        id: message.id,
        content: message.content,
        sender: { username: message.sender.username },
        createdAt: message.createdAt
      }
    }));
  }, [dispatch]);

  const handleMessageSent = useCallback((data: { tempId: string; messageId: string; status: string }) => {
    console.log(`Message sent: ${data.messageId} (temp: ${data.tempId})`);
    // The optimistic message will be updated by the sendMessage async thunk
  }, []);

  const handleUserTyping = useCallback((data: {
    userId: string;
    conversationId: string;
    isTyping: boolean
  }) => {
    dispatch(setTypingUsers({
      conversationId: data.conversationId,
      userId: data.userId,
      isTyping: data.isTyping
    }));
  }, [dispatch]);

  const handleUserStatusChange = useCallback((data: { userId: string; status: 'online' | 'offline' }) => {
    console.log(`User ${data.userId} is now ${data.status}`);
    // This could update user status in a separate slice if needed
  }, []);

  // Public API methods
  const sendMessage = useCallback((conversationId: string, content: string, messageType: string = 'TEXT'): string => {
    if (!socketRef.current || !user) {
      console.error('Socket not connected or user not available');
      return '';
    }

    const tempId = uuidv4();

    // Add optimistic message to Redux store
    dispatch(addOptimisticMessage({
      tempId,
      conversationId,
      content,
      sender: {
        id: user.id,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        profilePicture: user.profilePicture
      },
      messageType
    }));

    // Send message through socket
    socketRef.current.emit('send_message', {
      conversationId,
      content,
      messageType,
      tempId
    });

    return tempId;
  }, [dispatch, user]);

  const joinConversations = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('join_conversations');
    }
  }, []);

  const joinConversation = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('join_conversation', { conversationId });
    }
  }, []);

  const startTyping = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('typing_start', { conversationId });
    }
  }, []);

  const stopTyping = useCallback((conversationId: string) => {
    if (socketRef.current) {
      socketRef.current.emit('typing_stop', { conversationId });
    }
  }, []);

  const setUserOnline = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.emit('user_online');
    }
  }, []);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (socketRef.current) {
      console.log('Cleaning up socket connection...');

      // Remove all event listeners
      socketRef.current.off('connect');
      socketRef.current.off('disconnect');
      socketRef.current.off('error');
      socketRef.current.off('conversations_joined');
      socketRef.current.off('joined_conversation');
      socketRef.current.off('new_message');
      socketRef.current.off('message_sent');
      socketRef.current.off('user_typing');
      socketRef.current.off('user_status_change');

      // Disconnect socket
      socketRef.current.disconnect();
      socketRef.current = null;
    }
    setIsConnected(false);
  }, []);

  const contextValue: SocketContextType = {
    isConnected,
    sendMessage,
    joinConversations,
    joinConversation,
    startTyping,
    stopTyping,
    setUserOnline
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
```

### Step 12: Updated Component Integration

Now let's update the components to use the centralized SocketContext:

```typescript
// frontend/src/App.tsx - Wrap the application with SocketProvider
import React from 'react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';
import { store } from './store';
import AppRoutes from './routes/AppRoutes';

function App() {
  return (
    <Provider store={store}>
      <BrowserRouter>
        <AuthProvider>
          <SocketProvider>
            <div className="App">
              <AppRoutes />
            </div>
          </SocketProvider>
        </AuthProvider>
      </BrowserRouter>
    </Provider>
  );
}

export default App;
```

### Step 13: Example Usage in Other Components

Here's how other components would use the centralized SocketContext:

```typescript
// frontend/src/components/Chat/ChatRoom.tsx
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useSocket } from '../../contexts/SocketContext';
import { fetchMessages } from '../../store/slices/messageSlice';
import { RootState, AppDispatch } from '../../store';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { useAuth } from '../../contexts/AuthContext';

interface ChatRoomProps {
  conversationId: string;
}

const ChatRoom: React.FC<ChatRoomProps> = ({ conversationId }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { joinConversation, isConnected } = useSocket();
  const { user } = useAuth();

  const messages = useSelector((state: RootState) =>
    state.messages.messages[conversationId] || []
  );

  useEffect(() => {
    if (conversationId) {
      // Fetch message history from API
      dispatch(fetchMessages({ conversationId }));

      // Join the conversation room for real-time updates
      if (isConnected) {
        joinConversation(conversationId);
      }
    }
  }, [conversationId, dispatch, joinConversation, isConnected]);

  if (!user) {
    return <div>Please log in to access chat</div>;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Connection status */}
      {!isConnected && (
        <div className="bg-yellow-50 border-b border-yellow-200 px-4 py-2">
          <div className="flex items-center space-x-2 text-yellow-800 text-sm">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            <span>Reconnecting...</span>
          </div>
        </div>
      )}

      {/* Messages */}
      <MessageList
        conversationId={conversationId}
        currentUserId={user.id}
      />

      {/* Message input */}
      <MessageInput
        conversationId={conversationId}
        disabled={!isConnected}
      />
    </div>
  );
};

export default ChatRoom;
```

```typescript
// frontend/src/components/Chat/ConversationHeader.tsx
import React from 'react';
import { useSocket } from '../../contexts/SocketContext';
import { Icon } from '../ui/Icon';

interface ConversationHeaderProps {
  conversationName: string;
  participantCount: number;
}

const ConversationHeader: React.FC<ConversationHeaderProps> = ({
  conversationName,
  participantCount
}) => {
  const { isConnected } = useSocket();

  return (
    <div className="border-b border-gray-200 bg-white px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-lg font-semibold text-gray-900">
            {conversationName}
          </h1>
          <p className="text-sm text-gray-500">
            {participantCount} participant{participantCount !== 1 ? 's' : ''}
          </p>
        </div>

        <div className="flex items-center space-x-2">
          {/* Connection indicator */}
          <div className={`flex items-center space-x-1 text-sm ${
            isConnected ? 'text-green-600' : 'text-yellow-600'
          }`}>
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-500' : 'bg-yellow-500 animate-pulse'
            }`}></div>
            <span>{isConnected ? 'Connected' : 'Connecting'}</span>
          </div>

          {/* Settings button */}
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <Icon name="settings" size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConversationHeader;
```

## Key Architectural Improvements Summary

This updated Phase 2 implementation incorporates significant architectural improvements for better code organization, maintainability, and scalability:

### 1. Socket Server Service Layer Architecture
- **Service Separation**: Business logic separated into dedicated service classes
  - `MessageService`: Handles all message-related operations
  - `ConversationService`: Manages conversation operations
- **Thin Event Handlers**: Socket events act as thin wrappers that delegate to services
- **Benefits**: Better testability, single responsibility principle, easier maintenance

### 2. Frontend Redux Toolkit Architecture
- **Redux Slices**: Modern Redux patterns with reduced boilerplate
  - `messageSlice`: Manages message state with async thunks
  - `conversationSlice`: Handles conversation state management
- **Async Thunks**: Proper handling of asynchronous operations
- **Benefits**: Centralized state management, predictable state updates, better debugging

### 3. Centralized SocketContext Architecture
- **Single Source of Truth**: SocketContext manages all socket connections and operations
- **Provider Pattern**: Clean dependency injection through React context
- **Centralized Event Handling**: All socket events handled within the context
- **Redux Integration**: Context dispatches Redux actions for state updates
- **Connection Management**: Built-in connection status tracking and reconnection handling
- **Benefits**: No scattered socket connections, easier testing, better maintainability, automatic cleanup

### 4. Enhanced Integration Patterns
- **Optimistic Updates**: Immediate UI feedback with fallback handling
- **Type Safety**: Full TypeScript integration throughout the stack
- **Error Handling**: Comprehensive error handling at all layers
- **Real-time Synchronization**: Seamless integration between socket events and Redux state

### 5. Database Access Pattern (from Phase 1)
- **Direct Database Access**: Socket server uses Prisma for database operations
- **Schema Validation**: Pydantic (Django) and Zod (Socket server) for consistent validation
- **Custom UI Components**: Tailwind CSS components without external dependencies

### 6. Code Organization Benefits
- **Maintainability**: Clear separation of concerns makes code easier to understand
- **Testability**: Each layer can be tested independently
- **Scalability**: Service layer architecture supports future feature additions
- **Type Safety**: TypeScript ensures consistency across all layers
- **Performance**: Optimistic updates and direct database access improve user experience

### Implementation Highlights
- **Socket Server**: Service layer with Prisma + Zod integration
- **Frontend**: SocketContext as single source of truth for socket operations
- **Redux Integration**: Centralized state management with SocketContext dispatching actions
- **UI Components**: Custom Tailwind components with SocketContext integration
- **Real-time Features**: Typing indicators, optimistic updates, live message delivery
- **Connection Management**: Automatic reconnection and connection status tracking
- **Error Handling**: Comprehensive error handling and loading states

This enhanced architecture provides a solid foundation for all messaging features while maintaining the Phase 1 architectural principles. The improved code organization will significantly benefit future development and maintenance efforts.

Ensure thorough testing of all architectural layers before proceeding to Phase 3 (Encryption).
