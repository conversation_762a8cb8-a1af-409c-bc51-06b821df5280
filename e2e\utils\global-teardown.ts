import { TestDataManager } from './test-data-manager';

async function globalTeardown() {
  console.log('🧹 Starting global teardown for E2E tests...');

  try {
    // Clean up test data
    const testDataManager = new TestDataManager();
    await testDataManager.cleanupTestData();

    // Clean up authentication state
    await cleanupAuthState();

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Error during global teardown:', error);
    // Don't throw error to avoid failing the test run
  }
}

async function cleanupAuthState() {
  console.log('🔐 Cleaning up authentication state...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    const authStatePath = path.join(__dirname, '../fixtures/auth-state.json');
    if (fs.existsSync(authStatePath)) {
      fs.unlinkSync(authStatePath);
      console.log('✅ Authentication state cleaned up');
    }
  } catch (error) {
    console.error('❌ Failed to cleanup authentication state:', error);
  }
}

export default globalTeardown;
