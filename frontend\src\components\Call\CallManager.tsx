// frontend/src/components/Call/CallManager.tsx
import React from 'react';
import { useCalling } from '../../contexts/CallingContext';
import { IncomingCallModal } from './IncomingCallModal';
import { ActiveCallModal } from './ActiveCallModal';

export const CallManager: React.FC = () => {
  const { activeCall, error, clearError } = useCalling();

  // Show error notification if there's an error
  React.useEffect(() => {
    if (error) {
      // You can replace this with a proper toast notification
      console.error('Call error:', error);
      
      // Auto-clear error after 5 seconds
      const timeout = setTimeout(() => {
        clearError();
      }, 5000);

      return () => clearTimeout(timeout);
    }
  }, [error, clearError]);

  // Show IncomingCallModal for incoming calls in ringing state (waiting for user to answer)
  const isIncomingCall = activeCall?.isIncoming && activeCall.status === 'ringing';
  
  // Show ActiveCallModal for:
  // 1. Outgoing calls (caller view) - during initiating, ringing, connecting, and active phases
  // 2. Incoming calls that have been answered - during connecting and active phases
  const isActiveCall = activeCall && (
    // For outgoing calls - show during all phases except when completely idle
    (!activeCall.isIncoming && ['initiating', 'ringing', 'connecting', 'active'].includes(activeCall.status)) ||
    // For incoming calls - show after answered (connecting/active phases)
    (activeCall.isIncoming && ['connecting', 'active'].includes(activeCall.status))
  );

  return (
    <>
      {/* Incoming call modal */}
      <IncomingCallModal isOpen={isIncomingCall} />
      
      {/* Active call modal */}
      <ActiveCallModal isOpen={isActiveCall} />
      
      {/* Error notification */}
      {error && (
        <div className="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50">
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <button
              onClick={clearError}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  );
};
