// frontend/src/test/integration/messageStatus.integration.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { SocketProvider } from '../../contexts/SocketContext';
import MessageList from '../../components/Chat/MessageList';
import MessageInput from '../../components/Chat/MessageInput';
import messageReducer from '../../store/slices/messageSlice';
import conversationReducer from '../../store/slices/conversationSlice';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock socket.io-client
const mockSocket = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  connect: vi.fn(),
  disconnect: vi.fn(),
  connected: true,
  id: 'mock-socket-id'
};

vi.mock('socket.io-client', () => ({
  io: vi.fn(() => mockSocket)
}));

// Mock RTK Query API
vi.mock('../../services/messageApi', () => ({
  useGetMessagesQuery: vi.fn(() => ({
    data: {
      results: [],
      count: 0,
      next: null,
      previous: null
    },
    isLoading: false,
    error: null,
    refetch: vi.fn()
  })),
  useCreateConversationMutation: vi.fn(() => [
    vi.fn(),
    { isLoading: false }
  ])
}));

// Mock auth context
const mockUser = {
  id: 'user-1',
  username: 'testuser',
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
  profilePicture: null
};

const mockAuthContext = {
  user: mockUser,
  token: 'mock-token',
  isAuthenticated: true,
  login: vi.fn(),
  logout: vi.fn(),
  register: vi.fn(),
  loading: false
};

vi.mock('../../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => mockAuthContext
}));

describe('Message Status Integration Tests', () => {
  let store: ReturnType<typeof configureStore>;
  
  const TestComponent = () => (
    <Provider store={store}>
      <AuthProvider>
        <SocketProvider>
          <div>
            <MessageList
              conversationId="conv-1"
              currentUserId={mockUser.id}
            />
            <MessageInput
              conversationId="conv-1"
            />
          </div>
        </SocketProvider>
      </AuthProvider>
    </Provider>
  );

  beforeEach(() => {
    store = configureStore({
      reducer: {
        messages: messageReducer,
        conversations: conversationReducer
      }
    });
    
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should handle complete message sending flow with status updates', async () => {
    render(<TestComponent />);

    // Find the message input
    const messageInput = screen.getByPlaceholderText(/type a message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    // Type a message
    fireEvent.change(messageInput, { target: { value: 'Hello world!' } });
    
    // Send the message
    fireEvent.click(sendButton);

    // Verify socket emit was called for sending message
    expect(mockSocket.emit).toHaveBeenCalledWith('send_message', expect.objectContaining({
      conversationId: 'conv-1',
      content: 'Hello world!',
      messageType: 'TEXT',
      tempId: expect.any(String)
    }));

    // Simulate message_sent event from server
    const sendMessageCall = mockSocket.emit.mock.calls.find(call => call[0] === 'send_message');
    const tempId = sendMessageCall?.[1]?.tempId;

    // Simulate server response
    const messageId = 'msg-1';
    const messageSentHandler = mockSocket.on.mock.calls.find(call => call[0] === 'message_sent')?.[1];
    
    if (messageSentHandler) {
      messageSentHandler({
        tempId,
        messageId,
        status: 'DELIVERED'
      });
    }

    // Wait for UI to update
    await waitFor(() => {
      const state = store.getState();
      expect(state.messages.messageStatuses[messageId]).toBe('DELIVERED');
    });

    // Simulate message being read by another user
    const messageStatusHandler = mockSocket.on.mock.calls.find(call => call[0] === 'message_status_updated')?.[1];
    
    if (messageStatusHandler) {
      messageStatusHandler({
        messageId,
        userId: 'other-user',
        status: 'READ',
        updatedAt: new Date().toISOString()
      });
    }

    // Verify status updated to READ
    await waitFor(() => {
      const state = store.getState();
      expect(state.messages.messageStatuses[messageId]).toBe('READ');
    });
  });

  it('should handle message failure and retry flow', async () => {
    render(<TestComponent />);

    const messageInput = screen.getByPlaceholderText(/type a message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });

    // Send a message
    fireEvent.change(messageInput, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);

    const sendMessageCall = mockSocket.emit.mock.calls.find(call => call[0] === 'send_message');
    const tempId = sendMessageCall?.[1]?.tempId;

    // Simulate message failure
    const messageFailedHandler = mockSocket.on.mock.calls.find(call => call[0] === 'message_failed')?.[1];
    
    if (messageFailedHandler) {
      messageFailedHandler({
        tempId,
        error: 'Network error'
      });
    }

    // Wait for failure state
    await waitFor(() => {
      const state = store.getState();
      expect(state.messages.failedMessages[tempId]).toBe(true);
    });

    // Should show retry button
    await waitFor(() => {
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });

    // Click retry button
    const retryButton = screen.getByText('Retry');
    fireEvent.click(retryButton);

    // Verify retry emit was called
    expect(mockSocket.emit).toHaveBeenCalledWith('send_message', expect.objectContaining({
      conversationId: 'conv-1',
      content: 'Test message',
      messageType: 'TEXT',
      tempId: expect.any(String),
      retryOf: expect.any(String)
    }));
  });

  it('should handle typing indicators correctly', async () => {
    render(<TestComponent />);

    const messageInput = screen.getByPlaceholderText(/type a message/i);

    // Start typing
    fireEvent.change(messageInput, { target: { value: 'H' } });

    // Should emit typing start
    await waitFor(() => {
      expect(mockSocket.emit).toHaveBeenCalledWith('typing_start', {
        conversationId: 'conv-1'
      });
    });

    // Simulate receiving typing indicator from another user
    const userTypingHandler = mockSocket.on.mock.calls.find(call => call[0] === 'user_typing')?.[1];
    
    if (userTypingHandler) {
      userTypingHandler({
        userId: 'other-user',
        conversationId: 'conv-1',
        isTyping: true
      });
    }

    // Should show typing indicator
    await waitFor(() => {
      expect(screen.getByText(/typing/i)).toBeInTheDocument();
    });

    // Stop typing
    fireEvent.change(messageInput, { target: { value: '' } });

    // Should emit typing stop after timeout
    await waitFor(() => {
      expect(mockSocket.emit).toHaveBeenCalledWith('typing_stop', {
        conversationId: 'conv-1'
      });
    }, { timeout: 2000 });
  });

  it('should handle message read receipts when messages come into view', async () => {
    // Add a message from another user to the store
    const otherUserMessage = {
      id: 'msg-other',
      conversationId: 'conv-1',
      sender: {
        id: 'other-user',
        username: 'otheruser',
        first_name: 'Other',
        last_name: 'User',
        profile_picture: null
      },
      content: 'Hello from other user',
      messageType: 'TEXT' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    store.dispatch({ type: 'messages/addMessage', payload: otherUserMessage });

    render(<TestComponent />);

    // Message should be visible
    expect(screen.getByText('Hello from other user')).toBeInTheDocument();

    // Should emit message_read for the other user's message
    await waitFor(() => {
      expect(mockSocket.emit).toHaveBeenCalledWith('message_read', {
        messageId: 'msg-other'
      });
    });
  });

  it('should handle connection status changes', async () => {
    render(<TestComponent />);

    // Simulate disconnect
    const disconnectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'disconnect')?.[1];
    
    if (disconnectHandler) {
      disconnectHandler();
    }

    // Should disable message input when disconnected
    await waitFor(() => {
      const messageInput = screen.getByPlaceholderText(/type a message/i);
      expect(messageInput).toBeDisabled();
    });

    // Simulate reconnect
    const connectHandler = mockSocket.on.mock.calls.find(call => call[0] === 'connect')?.[1];
    
    if (connectHandler) {
      connectHandler();
    }

    // Should re-enable message input when connected
    await waitFor(() => {
      const messageInput = screen.getByPlaceholderText(/type a message/i);
      expect(messageInput).not.toBeDisabled();
    });
  });

  describe('End-to-End Message Status Flow', () => {
    it('should handle complete message lifecycle', async () => {
      render(<TestComponent />);

      // 1. Send a message
      const messageInput = screen.getByPlaceholderText(/type a message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      fireEvent.change(messageInput, { target: { value: 'E2E test message' } });
      fireEvent.click(sendButton);

      // 2. Verify optimistic update shows loading state
      await waitFor(() => {
        expect(screen.getByText('Sending...')).toBeInTheDocument();
      });

      // 3. Simulate server acknowledgment
      const sendMessageCall = mockSocket.emit.mock.calls.find(call => call[0] === 'send_message');
      const tempId = sendMessageCall?.[1]?.tempId;
      const messageId = 'e2e-msg-1';

      const messageSentHandler = mockSocket.on.mock.calls.find(call => call[0] === 'message_sent')?.[1];
      if (messageSentHandler) {
        messageSentHandler({
          tempId,
          messageId,
          status: 'DELIVERED'
        });
      }

      // 4. Verify delivered status is shown
      await waitFor(() => {
        const state = store.getState();
        expect(state.messages.messageStatuses[messageId]).toBe('DELIVERED');
      });

      // 5. Simulate another user reading the message
      const messageStatusHandler = mockSocket.on.mock.calls.find(call => call[0] === 'message_status_updated')?.[1];
      if (messageStatusHandler) {
        messageStatusHandler({
          messageId,
          userId: 'reader-user',
          status: 'READ',
          updatedAt: new Date().toISOString()
        });
      }

      // 6. Verify read status is shown
      await waitFor(() => {
        const state = store.getState();
        expect(state.messages.messageStatuses[messageId]).toBe('READ');
      });
    });

    it('should handle message timeout and failure recovery', async () => {
      render(<TestComponent />);

      // Send a message
      const messageInput = screen.getByPlaceholderText(/type a message/i);
      const sendButton = screen.getByRole('button', { name: /send/i });

      fireEvent.change(messageInput, { target: { value: 'Timeout test' } });
      fireEvent.click(sendButton);

      const sendMessageCall = mockSocket.emit.mock.calls.find(call => call[0] === 'send_message');
      const tempId = sendMessageCall?.[1]?.tempId;

      // Simulate message failure
      const messageFailedHandler = mockSocket.on.mock.calls.find(call => call[0] === 'message_failed')?.[1];
      if (messageFailedHandler) {
        messageFailedHandler({
          tempId,
          error: 'Request timeout'
        });
      }

      // Verify failure state
      await waitFor(() => {
        const state = store.getState();
        expect(state.messages.failedMessages[tempId]).toBe(true);
      });

      // Retry the message
      const retryButton = await screen.findByText('Retry');
      fireEvent.click(retryButton);

      // Verify retry attempt
      expect(mockSocket.emit).toHaveBeenCalledWith('send_message', expect.objectContaining({
        content: 'Timeout test',
        retryOf: expect.any(String)
      }));
    });
  });
});
