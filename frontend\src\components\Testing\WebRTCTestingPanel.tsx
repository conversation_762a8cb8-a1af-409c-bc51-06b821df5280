// frontend/src/components/Testing/WebRTCTestingPanel.tsx
import React, { useState, useEffect } from 'react';
import {
  Settings,
  Phone,
  Video,
  PhoneOff,
  Mic,
  MicOff,
  VideoOff,
  Play,
  Square,
  AlertCircle,
  CheckCircle,
  XCircle,
  Monitor,
  Headphones,
  Camera,
  Wifi,
  WifiOff,
  TestTube,
  Activity
} from 'lucide-react';
import { useSocket } from '../../contexts/SocketContext';
import { useCalling } from '../../contexts/CallingContext';
import { WebRTCManager } from '../../utils/webrtc';

interface TestingPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

interface MockCallData {
  callId: string;
  caller: {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
  };
  callType: 'audio' | 'video';
  conversationId: string;
}

export const WebRTCTestingPanel: React.FC<TestingPanelProps> = ({ isOpen, onClose }) => {
  const { socket } = useSocket();
  const { activeCall } = useCalling();
  const [mockMode, setMockMode] = useState(() => {
    return localStorage.getItem('webrtc_mock_mode') === 'true' ||
           import.meta.env.VITE_WEBRTC_MOCK_MODE === 'true' ||
           window.location.search.includes('mock=true');
  });
  const [mediaAvailability, setMediaAvailability] = useState<{
    audio: boolean;
    video: boolean;
    error?: string;
    checking: boolean;
  }>({ audio: false, video: false, checking: true });
  const [socketEvents, setSocketEvents] = useState<Array<{
    timestamp: string;
    event: string;
    data: any;
    direction: 'incoming' | 'outgoing';
  }>>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'connecting'>('disconnected');
  const [testCallData, setTestCallData] = useState<MockCallData>({
    callId: 'test-call-' + Date.now(),
    caller: {
      id: 'test-user-1',
      username: 'testuser1',
      firstName: 'Test',
      lastName: 'User'
    },
    callType: 'audio',
    conversationId: 'test-conversation-1'
  });

  // Check media availability
  useEffect(() => {
    const checkMedia = async () => {
      if (!socket) return;
      
      try {
        const webrtcManager = new WebRTCManager(socket, { mockMode });
        const availability = await webrtcManager.checkMediaAvailability(true);
        setMediaAvailability({
          ...availability,
          checking: false
        });
      } catch (error) {
        console.error('Failed to check media availability:', error);
        setMediaAvailability({
          audio: false,
          video: false,
          error: 'Failed to check media devices',
          checking: false
        });
      }
    };

    checkMedia();
  }, [socket, mockMode]);

  // Monitor socket connection
  useEffect(() => {
    if (!socket) {
      setConnectionStatus('disconnected');
      return;
    }

    const handleConnect = () => setConnectionStatus('connected');
    const handleDisconnect = () => setConnectionStatus('disconnected');
    const handleConnecting = () => setConnectionStatus('connecting');

    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('connecting', handleConnecting);

    // Set initial status
    setConnectionStatus(socket.connected ? 'connected' : 'disconnected');

    return () => {
      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
      socket.off('connecting', handleConnecting);
    };
  }, [socket]);

  // Monitor socket events for testing
  useEffect(() => {
    if (!socket) return;

    const logEvent = (event: string, direction: 'incoming' | 'outgoing') => (data: any) => {
      setSocketEvents(prev => [
        {
          timestamp: new Date().toLocaleTimeString(),
          event,
          data,
          direction
        },
        ...prev.slice(0, 49) // Keep last 50 events
      ]);
    };

    // Monitor calling-related events
    const callingEvents = [
      'incoming_call',
      'call_answered',
      'call_declined',
      'call_ended',
      'webrtc_offer',
      'webrtc_answer',
      'webrtc_ice_candidate',
      'toggle_audio',
      'toggle_video',
      'call_quality_report'
    ];

    callingEvents.forEach(event => {
      socket.on(event, logEvent(event, 'incoming'));
    });

    return () => {
      callingEvents.forEach(event => {
        socket.off(event, logEvent(event, 'incoming'));
      });
    };
  }, [socket]);

  const toggleMockMode = () => {
    const newMockMode = !mockMode;
    setMockMode(newMockMode);
    localStorage.setItem('webrtc_mock_mode', newMockMode.toString());
    
    // Show reload notification
    alert('Mock mode changed. Please reload the page for changes to take effect.');
  };

  const simulateIncomingCall = () => {
    if (!socket) return;
    
    const callData = {
      ...testCallData,
      callId: 'test-call-' + Date.now(),
      timestamp: new Date().toISOString()
    };
    
    // Emit the event as if it came from the server
    socket.emit('incoming_call', callData);
    
    // Log the outgoing event
    setSocketEvents(prev => [{
      timestamp: new Date().toLocaleTimeString(),
      event: 'incoming_call (simulated)',
      data: callData,
      direction: 'outgoing'
    }, ...prev.slice(0, 49)]);
  };

  const simulateCallAnswer = () => {
    if (!socket || !activeCall) return;
    
    const answerData = {
      callId: activeCall.id,
      timestamp: new Date().toISOString()
    };
    
    socket.emit('answer_call', answerData);
    
    setSocketEvents(prev => [{
      timestamp: new Date().toLocaleTimeString(),
      event: 'answer_call (simulated)',
      data: answerData,
      direction: 'outgoing'
    }, ...prev.slice(0, 49)]);
  };

  const simulateCallEnd = () => {
    if (!socket || !activeCall) return;
    
    const endData = {
      callId: activeCall.id,
      reason: 'user_ended',
      timestamp: new Date().toISOString()
    };
    
    socket.emit('end_call', endData);
    
    setSocketEvents(prev => [{
      timestamp: new Date().toLocaleTimeString(),
      event: 'end_call (simulated)',
      data: endData,
      direction: 'outgoing'
    }, ...prev.slice(0, 49)]);
  };

  const simulateWebRTCOffer = () => {
    if (!socket || !activeCall) return;
    
    const offerData = {
      callId: activeCall.id,
      offer: {
        type: 'offer',
        sdp: 'v=0\r\no=- 123456789 123456789 IN IP4 127.0.0.1\r\n...'
      },
      from: testCallData.caller.id
    };
    
    socket.emit('webrtc_offer', offerData);
    
    setSocketEvents(prev => [{
      timestamp: new Date().toLocaleTimeString(),
      event: 'webrtc_offer (simulated)',
      data: offerData,
      direction: 'outgoing'
    }, ...prev.slice(0, 49)]);
  };

  const clearEventLog = () => {
    setSocketEvents([]);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <TestTube className="w-6 h-6" />
            <h2 className="text-xl font-semibold">WebRTC Testing Panel</h2>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <XCircle className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* System Status */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <Monitor className="w-5 h-5" />
                <span>System Status</span>
              </h3>
              
              {/* Connection Status */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">Socket Connection</span>
                  <div className="flex items-center space-x-2">
                    {connectionStatus === 'connected' ? (
                      <><Wifi className="w-4 h-4 text-green-500" /><span className="text-green-600">Connected</span></>
                    ) : connectionStatus === 'connecting' ? (
                      <><Activity className="w-4 h-4 text-yellow-500 animate-pulse" /><span className="text-yellow-600">Connecting</span></>
                    ) : (
                      <><WifiOff className="w-4 h-4 text-red-500" /><span className="text-red-600">Disconnected</span></>
                    )}
                  </div>
                </div>
              </div>

              {/* Mock Mode Toggle */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">Mock Mode</span>
                  <button
                    onClick={toggleMockMode}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      mockMode
                        ? 'bg-green-100 text-green-800 hover:bg-green-200'
                        : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                    }`}
                  >
                    {mockMode ? 'Enabled' : 'Disabled'}
                  </button>
                </div>
                <p className="text-sm text-gray-600">
                  {mockMode 
                    ? '🎭 Mock streams will be used instead of real camera/microphone'
                    : 'Real media devices will be used for calls'
                  }
                </p>
              </div>

              {/* Media Availability */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-3">Media Device Status</h4>
                {mediaAvailability.checking ? (
                  <div className="flex items-center space-x-2">
                    <Activity className="w-4 h-4 animate-spin" />
                    <span className="text-gray-600">Checking devices...</span>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Headphones className="w-4 h-4" />
                        <span>Microphone</span>
                      </div>
                      {mediaAvailability.audio ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-500" />
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Camera className="w-4 h-4" />
                        <span>Camera</span>
                      </div>
                      {mediaAvailability.video ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-500" />
                      )}
                    </div>
                    {mediaAvailability.error && (
                      <div className="flex items-start space-x-2 mt-2 p-2 bg-red-50 rounded">
                        <AlertCircle className="w-4 h-4 text-red-500 mt-0.5" />
                        <span className="text-sm text-red-600">{mediaAvailability.error}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Active Call Status */}
              {activeCall && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2 flex items-center space-x-2">
                    <Phone className="w-4 h-4" />
                    <span>Active Call</span>
                  </h4>
                  <div className="space-y-1 text-sm">
                    <div>ID: {activeCall.id}</div>
                    <div>Type: {activeCall.type}</div>
                    <div>Status: {activeCall.status}</div>
                    <div>Connection: {activeCall.connectionState}</div>
                  </div>
                </div>
              )}
            </div>

            {/* Testing Controls */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <Play className="w-5 h-5" />
                <span>Test Controls</span>
              </h3>

              {/* Call Simulation */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-3">Call Simulation</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium">Call Type:</label>
                    <select
                      value={testCallData.callType}
                      onChange={(e) => setTestCallData(prev => ({ ...prev, callType: e.target.value as 'audio' | 'video' }))}
                      className="px-2 py-1 border rounded text-sm"
                    >
                      <option value="audio">Audio</option>
                      <option value="video">Video</option>
                    </select>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 mt-3">
                    <button
                      onClick={simulateIncomingCall}
                      disabled={!socket || connectionStatus !== 'connected'}
                      className="flex items-center justify-center space-x-2 px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-sm"
                    >
                      <Phone className="w-4 h-4" />
                      <span>Simulate Incoming</span>
                    </button>
                    
                    <button
                      onClick={simulateCallAnswer}
                      disabled={!socket || !activeCall || activeCall.status !== 'ringing'}
                      className="flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-sm"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span>Answer Call</span>
                    </button>
                    
                    <button
                      onClick={simulateCallEnd}
                      disabled={!socket || !activeCall}
                      className="flex items-center justify-center space-x-2 px-3 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-sm"
                    >
                      <PhoneOff className="w-4 h-4" />
                      <span>End Call</span>
                    </button>
                    
                    <button
                      onClick={simulateWebRTCOffer}
                      disabled={!socket || !activeCall}
                      className="flex items-center justify-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-sm"
                    >
                      <Activity className="w-4 h-4" />
                      <span>WebRTC Offer</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Socket Event Log */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">Socket Events Log</h4>
                  <button
                    onClick={clearEventLog}
                    className="text-sm text-gray-600 hover:text-gray-800"
                  >
                    Clear
                  </button>
                </div>
                <div className="max-h-64 overflow-y-auto space-y-1">
                  {socketEvents.length === 0 ? (
                    <div className="text-sm text-gray-500 text-center py-4">
                      No events logged yet. Try simulating a call or performing actions.
                    </div>
                  ) : (
                    socketEvents.map((event, index) => (
                      <div
                        key={index}
                        className={`text-xs p-2 rounded border-l-2 ${
                          event.direction === 'incoming'
                            ? 'bg-blue-50 border-blue-400'
                            : 'bg-green-50 border-green-400'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="font-medium">{event.event}</span>
                          <span className="text-gray-500">{event.timestamp}</span>
                        </div>
                        <pre className="text-gray-600 whitespace-pre-wrap">
                          {JSON.stringify(event.data, null, 2)}
                        </pre>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};