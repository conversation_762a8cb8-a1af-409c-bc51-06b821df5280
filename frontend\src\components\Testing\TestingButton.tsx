// frontend/src/components/Testing/TestingButton.tsx
import React, { useState } from 'react';
import { TestTube } from 'lucide-react';
import { WebRTCTestingPanel } from './WebRTCTestingPanel';

interface TestingButtonProps {
  className?: string;
}

export const TestingButton: React.FC<TestingButtonProps> = ({ className = '' }) => {
  const [isTestingPanelOpen, setIsTestingPanelOpen] = useState(false);

  // Only show in development or when testing mode is enabled
  const showTestingButton = 
    import.meta.env.DEV || 
    import.meta.env.VITE_SHOW_TESTING === 'true' ||
    localStorage.getItem('show_testing_tools') === 'true' ||
    window.location.search.includes('testing=true');

  if (!showTestingButton) {
    return null;
  }

  return (
    <>
      <button
        onClick={() => setIsTestingPanelOpen(true)}
        className={`fixed bottom-4 right-4 bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full shadow-lg transition-colors z-40 ${className}`}
        title="Open WebRTC Testing Panel"
      >
        <TestTube className="w-5 h-5" />
      </button>
      
      <WebRTCTestingPanel 
        isOpen={isTestingPanelOpen}
        onClose={() => setIsTestingPanelOpen(false)}
      />
    </>
  );
};