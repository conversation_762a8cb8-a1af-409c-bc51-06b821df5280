// frontend/src/hooks/useApiQuery.ts
import { useCallback } from 'react';
import { useApiError } from './useApiError';

interface UseApiQueryOptions {
  onError?: (error: any) => void;
  onSuccess?: (data: any) => void;
  retryOnError?: boolean;
  retryDelay?: number;
}

export const useApiQuery = <T>(
  queryResult: {
    data?: T;
    error?: any;
    isLoading: boolean;
    isError: boolean;
    isSuccess: boolean;
    refetch: () => void;
  },
  options: UseApiQueryOptions = {}
) => {
  const { formatError } = useApiError();
  const { onError, onSuccess, retryOnError = false, retryDelay = 1000 } = options;

  // Handle success callback
  if (queryResult.isSuccess && queryResult.data && onSuccess) {
    onSuccess(queryResult.data);
  }

  // Handle error callback
  if (queryResult.isError && queryResult.error && onError) {
    onError(formatError(queryResult.error));
  }

  const retry = useCallback(() => {
    if (retryOnError && queryResult.isError) {
      setTimeout(() => {
        queryResult.refetch();
      }, retryDelay);
    } else {
      queryResult.refetch();
    }
  }, [queryResult, retryOnError, retryDelay]);

  return {
    data: queryResult.data,
    error: queryResult.error ? formatError(queryResult.error) : null,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    isSuccess: queryResult.isSuccess,
    retry,
    refetch: queryResult.refetch,
  };
};
