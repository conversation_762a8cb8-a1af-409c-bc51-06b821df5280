# RTK Query Implementation Guide

This document outlines the complete RTK Query implementation for the chat application frontend, following the systematic approach documented in `RTK_approch_frontend.md`.

## 🏗️ Architecture Overview

The implementation follows a clean, modular architecture with clear separation of concerns:

```
src/
├── services/
│   ├── api.ts              # Main RTK Query API slice
│   ├── authApi.ts          # Authentication endpoints
│   ├── messageApi.ts       # Message endpoints
│   ├── conversationApi.ts  # Conversation endpoints
│   ├── userApi.ts          # User search endpoints
│   ├── cacheUtils.ts       # Cache management utilities
│   └── index.ts            # Centralized exports
├── hooks/
│   ├── useApiError.ts      # Error handling utilities
│   ├── useApiQuery.ts      # Query wrapper hook
│   ├── useApiMutation.ts   # Mutation wrapper hook
│   ├── useDebounce.ts      # Debouncing utility
│   └── useSocketCacheSync.ts # Socket-cache synchronization
├── components/ui/
│   ├── ErrorBoundary.tsx   # Error boundary component
│   ├── ApiErrorDisplay.tsx # API error display
│   └── LoadingSpinner.tsx  # Loading states
└── types/
    ├── index.ts            # Core types
    └── api.ts              # API-specific types
```

## 🔧 Key Features Implemented

### 1. **Systematic API Layer**
- ✅ Centralized API configuration with authentication
- ✅ Automatic token refresh handling
- ✅ Consistent error handling across all endpoints
- ✅ TypeScript interfaces for all requests/responses

### 2. **Clean Architecture**
- ✅ Modular service files for different API domains
- ✅ Reusable hooks for common patterns
- ✅ Separation of concerns between UI and data fetching
- ✅ Comprehensive TypeScript typing

### 3. **Reusable Components**
- ✅ Generic error handling components
- ✅ Loading state components
- ✅ Custom hooks for API interactions
- ✅ Debounced search functionality

### 4. **Reliable Implementation**
- ✅ Optimistic updates for better UX
- ✅ Cache invalidation strategies
- ✅ Real-time synchronization with WebSocket
- ✅ Error boundaries and fallback UI

## 📡 API Endpoints Implemented

### Authentication (`authApi.ts`)
- `login` - User login with token storage (no auth header required)
- `register` - User registration (no auth header required)
- `logout` - Client-side logout with cache clearing
- `getCurrentUser` - Get current user profile (requires auth header)

### Messages (`messageApi.ts`)
- `getMessages` - Fetch paginated messages
- `sendMessage` - Send message with optimistic updates
- `addMessageToCache` - Real-time message updates

### Conversations (`conversationApi.ts`)
- `getConversations` - Fetch user conversations
- `createConversation` - Create new conversation
- `updateConversationInCache` - Real-time updates
- `updateConversationLastMessage` - Update last message

### Users (`userApi.ts`)
- `searchUsers` - Debounced user search
- `getUserProfile` - Get user profile by ID
- `getCurrentUserProfile` - Get current user profile
- `updateUserProfile` - Update user profile

## 🔄 Cache Management

### Cache Tags
```typescript
tagTypes: [
  'User',        // User-related data
  'Conversation', // Conversation data
  'Message',     // Message data
  'Auth'         // Authentication data
]
```

### Cache Invalidation Strategy
- **Mutations automatically invalidate related data**
- **Real-time updates sync with cache**
- **Optimistic updates for immediate feedback**
- **Selective cache clearing for performance**

### Real-time Synchronization
The `useSocketCacheSync` hook automatically updates RTK Query cache when socket events are received:
- New messages update message cache
- Conversation updates sync with conversation cache
- User status changes update user data

## 🎯 Usage Examples

### Basic Query Usage
```typescript
import { useGetConversationsQuery } from '../services';

const ConversationList = () => {
  const { data, error, isLoading, refetch } = useGetConversationsQuery();
  
  if (isLoading) return <LoadingSpinner />;
  if (error) return <ApiErrorDisplay error={error} onRetry={refetch} />;
  
  return (
    <div>
      {data?.data?.results?.map(conversation => (
        <ConversationItem key={conversation.id} conversation={conversation} />
      ))}
    </div>
  );
};
```

### Mutation with Optimistic Updates
```typescript
import { useSendMessageMutation } from '../services';

const MessageInput = ({ conversationId }) => {
  const [sendMessage, { isLoading }] = useSendMessageMutation();
  
  const handleSend = async (content: string) => {
    try {
      await sendMessage({ conversationId, content });
      // Optimistic update is handled automatically
    } catch (error) {
      // Error handling is built-in
    }
  };
  
  return (
    <form onSubmit={(e) => {
      e.preventDefault();
      handleSend(inputValue);
    }}>
      <input disabled={isLoading} />
      <button type="submit" disabled={isLoading}>
        Send
      </button>
    </form>
  );
};
```

### Debounced Search
```typescript
import { useSearchUsersQuery } from '../services';
import { useDebounce } from '../hooks/useDebounce';

const UserSearch = () => {
  const [query, setQuery] = useState('');
  const debouncedQuery = useDebounce(query, 300);
  
  const { data, error, isLoading } = useSearchUsersQuery(debouncedQuery, {
    skip: debouncedQuery.length < 2,
  });
  
  return (
    <div>
      <input 
        value={query} 
        onChange={(e) => setQuery(e.target.value)}
        placeholder="Search users..."
      />
      {isLoading && <LoadingSpinner />}
      {error && <ApiErrorDisplay error={error} />}
      {data?.data?.map(user => (
        <UserItem key={user.id} user={user} />
      ))}
    </div>
  );
};
```

## 🔒 Error Handling

### Automatic Error Handling
- Network errors are caught and formatted
- Authentication errors trigger token refresh
- Validation errors are displayed with field details
- Server errors show appropriate fallback UI

### Custom Error Components
```typescript
<ApiErrorDisplay 
  error={error} 
  onRetry={refetch}
  size="sm"
  showRetry={true}
/>
```

## 🚀 Performance Optimizations

1. **Automatic Caching** - RTK Query caches all responses
2. **Request Deduplication** - Multiple identical requests are merged
3. **Background Refetching** - Data stays fresh automatically
4. **Selective Updates** - Only affected components re-render
5. **Optimistic Updates** - Immediate UI feedback
6. **Debounced Search** - Reduces API calls for search

## 🧪 Testing Considerations

The RTK Query implementation is designed to be easily testable:
- Mock API responses at the service level
- Test hooks in isolation
- Error states are predictable and testable
- Cache behavior is deterministic

## 🔄 Migration from Axios

The implementation maintains backward compatibility while providing:
- Better caching and performance
- Automatic loading and error states
- Real-time synchronization
- Type safety throughout
- Reduced boilerplate code

## 📝 Next Steps

1. **Add more endpoints** as needed
2. **Implement offline support** with RTK Query persistence
3. **Add request/response interceptors** for logging
4. **Implement retry logic** for failed requests
5. **Add performance monitoring** for API calls
