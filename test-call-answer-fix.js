#!/usr/bin/env node

/**
 * Test script to verify the call answer fix
 * This script simulates the call flow to test the fix
 */

const io = require('socket.io-client');

const SOCKET_URL = 'http://localhost:7000';
const TEST_USERS = {
  caller: {
    id: 'a001b1f5-c91c-4817-8ede-a3816e2db424',
    token: 'test-token-caller'
  },
  callee: {
    id: '8d3a49c3-4e67-4be0-b207-06165531abec', 
    token: 'test-token-callee'
  }
};

console.log('🧪 Testing Call Answer Fix');
console.log('===========================\n');

async function testCallFlow() {
  console.log('1. Connecting caller and callee...');
  
  // Connect caller
  const callerSocket = io(SOCKET_URL, {
    auth: {
      token: TEST_USERS.caller.token
    }
  });
  
  // Connect callee
  const calleeSocket = io(SOCKET_URL, {
    auth: {
      token: TEST_USERS.callee.token
    }
  });
  
  // Wait for connections
  await new Promise((resolve) => {
    let connected = 0;
    
    callerSocket.on('connect', () => {
      console.log('✅ Caller connected');
      connected++;
      if (connected === 2) resolve();
    });
    
    calleeSocket.on('connect', () => {
      console.log('✅ Callee connected');
      connected++;
      if (connected === 2) resolve();
    });
  });
  
  console.log('\n2. Setting up event listeners...');
  
  // Caller events
  callerSocket.on('call_initiated', (data) => {
    console.log('📞 Caller received call_initiated:', data);
  });
  
  callerSocket.on('call_answered', (data) => {
    console.log('📞 Caller received call_answered:', data);
  });
  
  callerSocket.on('call_error', (data) => {
    console.log('❌ Caller received call_error:', data);
  });
  
  // Callee events
  calleeSocket.on('incoming_call', (data) => {
    console.log('📞 Callee received incoming_call:', data);
    
    // Simulate answering the call after a delay
    setTimeout(() => {
      console.log('\n3. Callee answering call...');
      calleeSocket.emit('answer_call', { callId: data.callId });
    }, 2000);
  });
  
  calleeSocket.on('call_answered_success', (data) => {
    console.log('✅ Callee received call_answered_success:', data);
  });
  
  calleeSocket.on('call_error', (data) => {
    console.log('❌ Callee received call_error:', data);
  });
  
  console.log('\n4. Initiating call...');
  
  // Initiate call
  callerSocket.emit('initiate_call', {
    conversationId: '3f8c9263-6fa1-49ad-b9f2-6b49c52d955f',
    callType: 'audio'
  });
  
  // Wait for test to complete
  await new Promise((resolve) => {
    setTimeout(() => {
      console.log('\n5. Test completed');
      callerSocket.disconnect();
      calleeSocket.disconnect();
      resolve();
    }, 10000);
  });
}

// Check if socket server is running
async function checkSocketServer() {
  try {
    const testSocket = io(SOCKET_URL, { timeout: 2000 });
    
    await new Promise((resolve, reject) => {
      testSocket.on('connect', () => {
        console.log('✅ Socket server is running');
        testSocket.disconnect();
        resolve();
      });
      
      testSocket.on('connect_error', (error) => {
        reject(error);
      });
      
      setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 3000);
    });
  } catch (error) {
    console.error('❌ Socket server is not running on', SOCKET_URL);
    console.error('Please start the socket server first: cd socket-server && npm run dev');
    process.exit(1);
  }
}

async function main() {
  await checkSocketServer();
  await testCallFlow();
  
  console.log('\n🎯 Test Summary:');
  console.log('- If you see "call_answered_success", the fix is working');
  console.log('- If you see "call_error", there might still be an issue');
  console.log('- Check the socket server logs for detailed debugging info');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testCallFlow };
