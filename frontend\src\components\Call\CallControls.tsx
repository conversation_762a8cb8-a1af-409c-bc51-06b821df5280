// frontend/src/components/Call/CallControls.tsx
import React, { useState, useEffect } from 'react';
import { Phone, Video, AlertCircle } from 'lucide-react';
import { useCalling } from '../../contexts/CallingContext';
import { useSocket } from '../../contexts/SocketContext';
import { WebRTCManager } from '../../utils/webrtc';

interface CallControlsProps {
  conversationId: string;
  otherParticipant: {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
  };
  disabled?: boolean;
}

export const CallControls: React.FC<CallControlsProps> = ({
  conversationId,
  otherParticipant,
  disabled = false,
}) => {
  const { activeCall, initiateCall } = useCalling();
  const { socket } = useSocket();
  const [mediaAvailability, setMediaAvailability] = useState<{
    audio: boolean;
    video: boolean;
    error?: string;
    checking: boolean;
  }>({ audio: false, video: false, checking: true });

  // Check media device availability (without requesting permissions)
  useEffect(() => {
    const checkDevices = async () => {
      try {
        if (!navigator.mediaDevices) {
          setMediaAvailability({
            audio: false,
            video: false,
            error: 'Media devices not supported',
            checking: false
          });
          return;
        }

        const devices = await navigator.mediaDevices.enumerateDevices();
        const hasAudio = devices.some(device => device.kind === 'audioinput');
        const hasVideo = devices.some(device => device.kind === 'videoinput');

        setMediaAvailability({
          audio: hasAudio,
          video: hasVideo,
          checking: false
        });
      } catch (error) {
        console.error('Failed to check media devices:', error);
        setMediaAvailability({
          audio: false,
          video: false,
          error: 'Failed to check devices',
          checking: false
        });
      }
    };

    checkDevices();
  }, []);

  const handleAudioCall = async () => {
    if (disabled || activeCall) return;

    if (!mediaAvailability.audio) {
      console.error('No audio device available for call');
      return;
    }

    try {
      console.log('🎯 Initiating audio call with real media access');
      await initiateCall(conversationId, 'audio', otherParticipant);
    } catch (error) {
      console.error('Failed to initiate audio call:', error);
    }
  };

  const handleVideoCall = async () => {
    if (disabled || activeCall) return;

    if (!mediaAvailability.audio) {
      console.error('No audio device available for call');
      return;
    }

    if (!mediaAvailability.video) {
      console.error('No video device available for call');
      return;
    }

    try {
      console.log('🎯 Initiating video call with real media access');
      await initiateCall(conversationId, 'video', otherParticipant);
    } catch (error) {
      console.error('Failed to initiate video call:', error);
    }
  };

  const isCallInProgress = activeCall && activeCall.status !== 'idle';
  const { audio: hasAudio, video: hasVideo, error: mediaError, checking } = mediaAvailability;

  // Show loading state while checking media
  if (checking) {
    return (
      <div className="flex items-center space-x-2">
        <div className="animate-pulse bg-gray-200 rounded-lg w-9 h-9"></div>
        <div className="animate-pulse bg-gray-200 rounded-lg w-9 h-9"></div>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      {/* Show media error if any */}
      {mediaError && (
        <div className="text-xs text-red-500 mr-2" title={mediaError}>
          ⚠️
        </div>
      )}
      {/* Audio call button */}
      <button
        onClick={handleAudioCall}
        disabled={disabled || isCallInProgress || !hasAudio}
        className={`p-2 rounded-lg transition-colors ${
          disabled || isCallInProgress || !hasAudio
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
        }`}
        title={
          !hasAudio
            ? 'No audio device available'
            : 'Start audio call'
        }
        data-testid="audio-call-button"
      >
        <Phone className="w-5 h-5" />
      </button>

      {/* Video call button */}
      <button
        onClick={handleVideoCall}
        disabled={disabled || isCallInProgress || !hasAudio || !hasVideo}
        className={`p-2 rounded-lg transition-colors ${
          disabled || isCallInProgress || !hasAudio || !hasVideo
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
        }`}
        title={
          !hasAudio
            ? 'No audio device available'
            : !hasVideo
            ? 'No video device available'
            : 'Start video call'
        }
        data-testid="video-call-button"
      >
        <Video className="w-5 h-5" />
      </button>
    </div>
  );
};
