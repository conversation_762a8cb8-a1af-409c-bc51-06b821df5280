#!/usr/bin/env node

/**
 * Quick WebRTC Implementation Test
 * This script tests the WebRTC implementation without running full E2E tests
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Quick WebRTC Implementation Test');
console.log('===================================\n');

// Test 1: Verify implementation files exist and have correct content
console.log('1. Checking implementation files...');

const filesToCheck = [
  {
    path: 'frontend/src/contexts/CallingContext.tsx',
    checks: [
      'REAL WEBRTC MODE ENABLED for production',
      'answer_call socket event',
      'initializeCall'
    ]
  },
  {
    path: 'frontend/src/utils/webrtc.ts',
    checks: [
      'checkMediaPermissions',
      'Requesting real media access',
      'setupSocketListeners'
    ]
  },
  {
    path: 'socket-server/src/events/callingEvents.ts',
    checks: [
      'Caller.*joined call room',
      'WebRTC Offer received',
      'call room.*members'
    ]
  },
  {
    path: 'frontend/src/components/Call/CallControls.tsx',
    checks: [
      'enumerateDevices',
      'hasAudio',
      'hasVideo'
    ]
  }
];

let allChecksPass = true;

filesToCheck.forEach(file => {
  const fullPath = path.join(__dirname, file.path);
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ File not found: ${file.path}`);
    allChecksPass = false;
    return;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  
  file.checks.forEach(check => {
    const regex = new RegExp(check, 'i');
    if (regex.test(content)) {
      console.log(`✅ ${file.path}: ${check}`);
    } else {
      console.log(`❌ ${file.path}: Missing ${check}`);
      allChecksPass = false;
    }
  });
});

// Test 2: Check environment configuration
console.log('\n2. Checking environment configuration...');

const envFiles = [
  'frontend/.env.example',
  'frontend/.env.production'
];

envFiles.forEach(envFile => {
  const fullPath = path.join(__dirname, envFile);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, 'utf8');
    if (content.includes('VITE_WEBRTC_MOCK_MODE=false')) {
      console.log(`✅ ${envFile}: Mock mode disabled`);
    } else {
      console.log(`❌ ${envFile}: Mock mode configuration incorrect`);
      allChecksPass = false;
    }
  } else {
    console.log(`❌ ${envFile}: File not found`);
    allChecksPass = false;
  }
});

// Test 3: Check package.json scripts
console.log('\n3. Checking package.json scripts...');

const packageJsonPath = path.join(__dirname, 'frontend/package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  const requiredScripts = ['dev:mock', 'build:prod'];
  requiredScripts.forEach(script => {
    if (packageJson.scripts && packageJson.scripts[script]) {
      console.log(`✅ Script exists: ${script}`);
    } else {
      console.log(`❌ Script missing: ${script}`);
      allChecksPass = false;
    }
  });
} else {
  console.log('❌ frontend/package.json not found');
  allChecksPass = false;
}

// Test 4: Check documentation
console.log('\n4. Checking documentation...');

const docFiles = [
  'WEBRTC_REAL_MODE_IMPLEMENTATION.md',
  'frontend/src/test/webrtc-real-test.js'
];

docFiles.forEach(docFile => {
  const fullPath = path.join(__dirname, docFile);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ Documentation exists: ${docFile}`);
  } else {
    console.log(`❌ Documentation missing: ${docFile}`);
    allChecksPass = false;
  }
});

// Test 5: Check E2E test files
console.log('\n5. Checking E2E test files...');

const e2eFiles = [
  'e2e/tests/webrtc-calling.spec.ts',
  'e2e/scripts/setup-test-users.js',
  'test-webrtc-e2e.js'
];

e2eFiles.forEach(e2eFile => {
  const fullPath = path.join(__dirname, e2eFile);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ E2E test file exists: ${e2eFile}`);
  } else {
    console.log(`❌ E2E test file missing: ${e2eFile}`);
    allChecksPass = false;
  }
});

// Summary
console.log('\n📋 Test Summary');
console.log('===============');

if (allChecksPass) {
  console.log('✅ All implementation checks passed!');
  console.log('\n🚀 Next Steps:');
  console.log('1. Start all services:');
  console.log('   - Backend: cd backend && python manage.py runserver 6000');
  console.log('   - Frontend: cd frontend && npm run dev');
  console.log('   - Socket: cd socket-server && npm run dev');
  console.log('');
  console.log('2. Test manually:');
  console.log('   - Open http://localhost:5000 in two browser windows');
  console.log('   - <NAME_EMAIL> and <EMAIL>');
  console.log('   - Try audio/video calls between them');
  console.log('');
  console.log('3. Run E2E tests:');
  console.log('   node test-webrtc-e2e.js');
  console.log('');
  console.log('4. Test real WebRTC in browser console:');
  console.log('   testRealWebRTC()');
} else {
  console.log('❌ Some implementation checks failed!');
  console.log('Please review the failed checks above and fix them.');
  process.exit(1);
}

console.log('\n🎯 WebRTC Implementation Status:');
console.log('- ✅ Mock mode control implemented');
console.log('- ✅ Real media permission handling');
console.log('- ✅ Socket room joining for WebRTC signaling');
console.log('- ✅ UI updates for device availability');
console.log('- ✅ Comprehensive error handling');
console.log('- ✅ E2E testing framework');
console.log('- ✅ Documentation and guides');
