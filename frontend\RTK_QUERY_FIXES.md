# RTK Query Implementation Fixes

This document outlines the fixes applied to resolve the circular dependency and authorization header issues in the RTK Query implementation.

## Issues Fixed

### 1. Circular Dependency Error
**Problem**: `Uncaught ReferenceError: Cannot access 'api' before initialization`

**Root Cause**: The `api.ts` file was trying to re-export hooks from `authApi.ts` while `authApi.ts` was importing the `api` object from `api.ts`, creating a circular dependency.

**Solution**:
- Removed re-exports from `api.ts` (lines 90-93)
- Updated all components to import from `services/index.ts` instead of `services/api.ts`
- Maintained proper initialization order in the centralized export file

**Files Modified**:
- `frontend/src/services/api.ts` - Removed circular re-exports
- `frontend/src/contexts/AuthContext.tsx` - Updated import path
- `frontend/src/components/Chat/UserSearch.tsx` - Updated import path
- `frontend/src/hooks/useSocketCacheSync.ts` - Updated import path
- `frontend/src/store/index.ts` - Updated import path

### 2. API Endpoint Consistency
**Problem**: Authentication endpoints were inconsistent with backend implementation

**Issues Found**:
- `getCurrentUser` was calling `/api/auth/me/` (non-existent endpoint)
- `refreshToken` endpoint didn't exist on backend
- `logout` endpoint didn't exist on backend

**Solutions**:
- Fixed `getCurrentUser` to use `/api/auth/profile/` (existing endpoint)
- Removed `refreshToken` mutation (backend doesn't provide this endpoint)
- Modified `logout` to be client-side only with cache clearing
- Updated token expiration handling to redirect to login instead of attempting refresh

**Files Modified**:
- `frontend/src/services/authApi.ts` - Fixed endpoints and removed non-existent ones
- `frontend/src/services/index.ts` - Removed refresh token export
- `frontend/src/services/api.ts` - Simplified 401 error handling

### 3. Authorization Header Configuration
**Problem**: Authorization headers were being sent to all endpoints, including public ones

**Solution**:
- Enhanced `prepareHeaders` function to exclude authorization headers for public endpoints
- Added logic to identify public endpoints (`login`, `register`)
- Ensured all authenticated endpoints receive proper `Authorization: Bearer <token>` headers

**Implementation**:
```typescript
prepareHeaders: (headers, { endpoint }) => {
  // Don't add authorization headers for login and register endpoints
  const publicEndpoints = ['login', 'register'];
  const isPublicEndpoint = publicEndpoints.includes(endpoint);
  
  const token = localStorage.getItem('token');
  if (token && !isPublicEndpoint) {
    headers.set('authorization', `Bearer ${token}`);
  }
  headers.set('content-type', 'application/json');
  return headers;
}
```

## Backend Endpoints Verified

### Available Endpoints:
- `POST /api/auth/login/` - User login (public)
- `POST /api/auth/register/` - User registration (public)
- `GET /api/auth/profile/` - Get current user profile (authenticated)
- `GET /api/messaging/conversations/` - List conversations (authenticated)
- `POST /api/messaging/conversations/create/` - Create conversation (authenticated)
- `GET /api/messaging/conversations/{id}/messages/` - Get messages (authenticated)
- `POST /api/messaging/conversations/{id}/send/` - Send message (authenticated)
- `GET /api/messaging/users/search/` - Search users (authenticated)

### Non-existent Endpoints (Removed):
- `/api/auth/me/` - Replaced with `/api/auth/profile/`
- `/api/auth/token/refresh/` - Not implemented in backend
- `/api/auth/logout/` - Not implemented in backend

## Authentication Flow

### Current Implementation:
1. **Login/Register**: No auth headers, stores tokens on success
2. **Authenticated Requests**: Automatic `Authorization: Bearer <token>` header
3. **Token Expiration**: Clear tokens and redirect to login (no refresh)
4. **Logout**: Client-side token clearing and cache reset

### Token Management:
- Access tokens stored in `localStorage` as `token`
- Refresh tokens stored in `localStorage` as `refreshToken` (for future use)
- 401 responses trigger automatic token cleanup and login redirect

## Testing Recommendations

To verify the fixes work correctly:

1. **Test Login Flow**:
   ```javascript
   // Should work without authorization headers
   const { data } = await useLoginMutation()({ email, password });
   ```

2. **Test Authenticated Requests**:
   ```javascript
   // Should automatically include Authorization header
   const { data } = await useGetConversationsQuery();
   ```

3. **Test Token Expiration**:
   - Manually expire token in localStorage
   - Make authenticated request
   - Should redirect to login page

4. **Test Circular Dependencies**:
   - Import hooks from `services/index.ts`
   - Should not cause initialization errors

## Performance Impact

The fixes provide several performance benefits:
- Eliminated circular dependency reduces bundle size
- Proper authorization header handling reduces unnecessary auth attempts
- Client-side logout is faster than API calls
- Simplified token management reduces complexity

## Future Enhancements

If backend adds token refresh and logout endpoints:
1. Add refresh token endpoint to `authApi.ts`
2. Update base query to attempt token refresh on 401
3. Add proper logout endpoint call
4. Update documentation accordingly

## Compatibility

These fixes maintain backward compatibility with:
- Existing component interfaces
- Socket integration
- Cache management
- Error handling patterns
- TypeScript types
