// frontend/src/components/TailwindTest.tsx
import React from 'react';

export const TailwindTest: React.FC = () => {
  return (
    <div className="p-8 bg-blue-500 text-white rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold mb-4">Tailwind CSS Test</h1>
      <p className="text-lg">If you can see this styled correctly, Tailwind CSS is working!</p>
      <div className="mt-4 flex space-x-4">
        <button className="bg-green-500 hover:bg-green-600 px-4 py-2 rounded transition-colors">
          <PERSON> Button
        </button>
        <button className="bg-red-500 hover:bg-red-600 px-4 py-2 rounded transition-colors">
          Red Button
        </button>
      </div>
    </div>
  );
};
